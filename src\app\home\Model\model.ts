

export interface DateValidations {
  minDate?: string;  // Expected format: "YYYY-MM-DD"
  maxDate?: string;  // Expected format: "YYYY-MM-DD"
  noFuture?: boolean;
  noPast?: boolean;
}

export interface Style {
  [key: string]: any;
}

export interface FormJson {
  id?: string;
  auditHistory: {
    userName?: string;
    formName?: string;
    location?: string;
    createdBy?: string;
    updatedBy?: string;
    createdDate?: Date;
    updatedDate?: Date;
    status?: string;
    userID?: string;
  };
  component: FormSection[];
}
export interface FormSection {
  title: string;
  canCollapsed?: boolean;
  isCollapsed?: boolean;
  repeatable?: boolean;
  conditionalAtSection?: ConditionalFieldGroup;
  elements: FormComponent[];
  }



export type FormComponent =
  | TextComponent
  | SelectComponent
  | MapComponent
  | TextareaComponent
  | fileComponent
  | ImageComponent
  | DateComponent
  | SignatureComponent
  | QRScannerComponent
  | LinkComponent
  | ViewPdfComponent
  | UploadPdfComponent
  | TimerComponent;


export interface TextComponent  {
  type: 'text';
  conditionalFieldGroup?: ConditionalFieldGroup;
  attributes: {
    label: string;
    field_Id: string;
    is_required: boolean;
    placeholder_text?: string;
    show_label?: boolean;
    default_value?: string;
    style?: Style;
    actions?: ActionModel;
    uploadedFileName?: string;
    image_url?: string;
    validations?: any;

  };
}

export interface SelectComponent  {
  type: 'Select';
 conditionalFieldGroup?: ConditionalFieldGroup;
  multiselect: boolean;
  attributes: {
    label: string;
    field_Id: string;
    is_required: boolean;
    placeholder_text?: string;
    show_label?: boolean;
    default_value?: string;
    dataListId: string;
    dataSource?: DropdownData;
    actions?: ActionModel;
    uploadedFileName?: string;
    image_url?: string;
    validations?: any;
    style?: Style;
  };
}


export interface MapComponent  {
  type: 'map';
  conditionalFieldGroup?: ConditionalFieldGroup;
  attributes: {
    label: string;
    field_Id: string;
    is_required: boolean;
    show_label?: boolean;
    default_value?: string;
    style?: Style;
    default_lat?: number;
    default_lng?: number;
    actions?: ActionModel;
    uploadedFileName?: string;
    image_url?: string;
    validations?: any;
  };
}

export interface TextareaComponent  {
  type: 'textarea';
  conditionalFieldGroup?: ConditionalFieldGroup;
  attributes: {
    label: string;
    field_Id: string;
    is_required: boolean;
    placeholder_text?: string;
    default_value?: string;
    show_label?: boolean;
    actions?: ActionModel;
    uploadedFileName?: string;
    image_url?: string;
    style?: Style;
    validations?: any;
  };
}

export interface fileComponent  {
  type: 'file';
  conditionalFieldGroup?: ConditionalFieldGroup;
  attributes: {
    label: string;
    field_Id: string;
    is_required: boolean;
    show_label?: boolean;
    placeholder_text?: string;
    default_value?: string;
    actions?: ActionModel;
    style?: Style;
    uploadedFileName?: string;
    imageDisplayUrl?: string;
    image_url?: string;
    validations?: any;

  };
}

export interface ImageComponent {
  type: 'image';
  conditionalFieldGroup?: ConditionalFieldGroup;
  attributes: {
    label: string;
    field_Id?: string;
    is_required?: boolean;
    default_value?: string;
    show_label?: boolean;
    placeholder_text?: string;
    uploadedFileName?: string;
    imageDisplayUrl?: string;
    image_url?: string;
    alt_text?: string;
    style?: Style;
    actions?: ActionModel;
    validations?: any;
  };
}

export interface DateComponent  {
  type: 'date';
 conditionalFieldGroup?: ConditionalFieldGroup;
  attributes: {
    label: string;
    field_Id: string;
    is_required: boolean;
    default_value?: string;
    show_label?: boolean;
    actions?: ActionModel;
    style?: Style;
    uploadedFileName?: string;
    image_url?: string;
    validations?: any;
  };
}

export interface SignatureComponent  {
  type: 'signature';
  conditionalFieldGroup?: ConditionalFieldGroup;
  attributes: {
    label: string;
    field_Id: string;
    default_value?: string;
    is_required: boolean;
    show_label?: boolean;
    pen_color?: string;
    actions?: ActionModel;
    style?: Style;
    uploadedFileName?: string;
    image_url?: string;
    validations?: any;
  };
}

export interface QRScannerComponent {
  type: 'qrscanner';
  conditionalFieldGroup?: ConditionalFieldGroup;
  attributes: {
    label: string;
    field_Id: string;
    placeholder_text?: string;
    default_value?: string;
    is_required: boolean;
    show_label?: boolean;
    actions?: ActionModel;
    style?: Style;
    uploadedFileName?: string;
    image_url?: string;
    validations?: any;
  };
}

export interface LinkComponent  {
  type: 'link';
  conditionalFieldGroup?: ConditionalFieldGroup;
  attributes: {
    label: string;
    is_required?: boolean;
    field_Id?: string;
    show_label?: boolean;
    default_value?: string;
    url: string;
    style?: Style;
    link_text: string;
    actions?: ActionModel;
    uploadedFileName?: string;
    image_url?: string;
    validations?: any;
  };
}

export interface ViewPdfComponent  {
  type: 'pdfviewer';
  conditionalFieldGroup?: ConditionalFieldGroup;
  attributes: {
    label: string;
    field_Id?: string;
    is_required: boolean;
    show_label?: boolean;
    placeholder_text?: string;
    default_value?: string;
    pdf_url?: string;
    actions?: ActionModel;
    uploadedFileName?: string;
    image_url?: string;
    style?: Style;
    validations?: any;
  };
}

export interface UploadPdfComponent  {
  type: 'PDFfile';
  conditionalFieldGroup?: ConditionalFieldGroup;
  attributes: {
    label: string;
    field_Id: string;
    is_required: boolean;
    show_label?: boolean;
    default_value?: string;
    placeholder_text?: string;
    actions?: ActionModel;
    style?: Style;
    uploadedFileName?: string;
    image_url?: string;
    validations?: any;
  };
}

export interface TimerComponent  {
  type: 'time';
  conditionalFieldGroup?: ConditionalFieldGroup;
  attributes: {
    label: string;
    field_Id: string;
    is_required: boolean;
    show_label?: boolean;
    default_value?: string;
    placeholder_text?: string;
    actions?: ActionModel;
    style?: Style;
    uploadedFileName?: string;
    image_url?: string;
    validations?: any;
  };
}








































export interface DropdownData {
  id?: string;
  list: FormListData[];
  auditHistory: {
    userName?: string;
    listName?: string;
    location?: string;
    createdBy?: string;
    updatedBy?: string;
    createdDate?: Date;
    updatedDate?: Date;
    status?: string;
    userID?: string;
  };
}

export interface FormListData {
  title?: string;
  items: ListValue[];
}

export interface ListValue {
  value: string;
}


export interface ActionModel {
  comment?: boolean; // Define the properties of ActionModeL
  camera?: boolean;
  flag?: boolean;
  followupForm?: {
    formId: string;
    formName: string;
  };
}

export interface ConditionalFieldGroup {
  type: string,
  logic: string; // 'AND' or 'OR'
  conditions: Condition[];
}

export interface Condition {
  field: string;
  operator: '==' | '!=';
  valueType?: 'Value' | 'Field' | 'Expression'; // optional if needed
  value: any;
}

export interface FormSubmission {
  id?: string;
  formTemplateId?: string;
  auditHistory: {
    createdBy?: string;
    createdDate?: Date;
    updatedBy?: string;
    updatedDate?: Date;
    location?: string;
    status?: string;
    userID?: string;
  };
  formData: any;
}

