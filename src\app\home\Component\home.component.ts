import { Component, ElementRef, Input, OnChanges, SimpleChanges, ViewChild, OnDestroy, EventEmitter, Output } from '@angular/core';
import { FormGroup, FormBuilder, Validators, ValidatorFn, AbstractControl, ValidationErrors, FormControl } from '@angular/forms';
import SignaturePad from 'signature_pad';
import { FormComponent, FormJson, FormSection, FormSubmission } from '../Model/model';
import { CoreDataService } from '../../core-data.service';
import { BarcodeFormat } from '@zxing/library';
import { Router, ActivatedRoute } from '@angular/router';
import { ShareService } from '../../SharedData/share-services.service';
import { OneDriveService, OneDriveFolder } from '../../SharedData/OneDriveService';
import { SignatureService } from '../../SharedData/signature.service';
import { Subscription } from 'rxjs';
import { MsalService } from '@azure/msal-angular';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { firstValueFrom } from 'rxjs';
import { FormHtmlViewComponent } from './form-html-view.component';
import { MsalAuthService } from '../../SharedData/MsalAuthService';

@Component({
  selector: 'app-home',
  templateUrl: '../Template/home.component.html',
  styleUrl: '../Style/home.component.css',
  standalone: false
})
export class HomeComponent implements OnChanges, OnDestroy {
  @Input() formId: string = '';
  @Input() submissionId: string = '';


  // these Both for Mini form Values
  @ViewChild('miniFormRef') miniFormRef: HomeComponent | undefined;
  @Input() MainformJson: FormJson | null = null;
  @Input() showSubmitButton: boolean = true; // default is true
  @Input() MiniFormGroup: FormGroup | null = null;
  @Output() formSubmit = new EventEmitter<any>();



  isLoading: boolean = false;
  previewImageUrl: string = '';
  // Store signature data from submission for delayed application
  pendingSignatureData: string | null = null;
  formats: BarcodeFormat[] = [
    BarcodeFormat.QR_CODE,
    BarcodeFormat.CODE_128,
    BarcodeFormat.DATA_MATRIX
  ];

  @ViewChild('signaturePad', { static: false }) signaturePadElement!: ElementRef;
  @ViewChild('managerSignaturePad', { static: false }) managerSignaturePadElement!: ElementRef;
  @ViewChild('formHtmlView') formHtmlView!: FormHtmlViewComponent;
  visibleComments: { [key: string]: boolean } = {};
  selectedFollowUp: { [fieldName: string]: string } = {}; // in follow up user select a Items
  visibleflag: { [key: string]: boolean} = {};

  // Subscriptions to manage
  private subscriptions: Subscription[] = [];

  signatureColor: string = 'black';
  signaturePadOptions = {
    minWidth: 1,
    canvasWidth: 400,
    canvasHeight: 150
  };

  // Manager signature properties
  managerSignaturePad: SignaturePad | null = null;
  managerSignatureColor: string = 'black';
  managerSignatureSaved: boolean = false;
  showManagerSignatureModal: boolean = false;
  tempManagerName: string = '';
  managerSignatures: Array<{id: string, name: string, signature: string, timestamp: string}> = [];
  formGroup : FormGroup;
  constructor(
    private fb: FormBuilder,
    protected coreDataService: CoreDataService,
    private router: Router,
    private route: ActivatedRoute,
    private http: HttpClient,
    public shareService: ShareService,
    private msalService: MsalService,
    private oneDriveService: OneDriveService,
    public signatureService: SignatureService,
    private msalAuth: MsalAuthService
  ) {
    this.formGroup = this.fb.group({});
    this.miniFormGroup = this.fb.group({});

    // Subscribe to signature data changes
    this.subscriptions.push(
      this.signatureService.signatureData$.subscribe(data => {
        if (data && this.formGroup.get('signature')) {
          this.formGroup.get('signature')?.setValue(data);
        }
      })
    );
  }

  ngOnDestroy(): void {
    // Unsubscribe from all subscriptions to prevent memory leaks
    this.subscriptions.forEach(sub => sub.unsubscribe());
  }
  uploadedImages: { [key: string]: string | ArrayBuffer | null } = {};
  guidanceImages: { [key: string]: string } = {}; // Store guidance image paths
  demo2!:FormJson; //Main form JSON
  followUpform!:FormJson; //followUp form JSON
  tempData!: FormJson; //temp form JSON

  ngOnChanges(changes: SimpleChanges): void {
    // Check if we need to reset the form
    const needsReset =
      (changes['formId'] && changes['formId'].previousValue !== changes['formId'].currentValue) ||
      (changes['submissionId'] && changes['submissionId'].previousValue && !changes['submissionId'].currentValue);

    if (needsReset) {
      // console.log('Form needs reset due to ID changes');
      this.resetForm();
    }

    // Handle form ID changes
    if (changes['formId'] && changes['formId'].currentValue) {
      this.loadForm(changes['formId'].currentValue);
    } else if (changes['formId'] && !changes['formId'].currentValue) {
      console.log('Form ID cleared, waiting for new form ID');
      // If the form ID was cleared, we'll wait for it to be set again
    }

    // Handle submission ID changes
    if (changes['submissionId'] && changes['submissionId'].currentValue) {
      // Only load submission data if we have a form loaded
      if (this.demo2 && this.demo2.id) {
        this.loadSubmissionData(changes['submissionId'].currentValue);
      } else {
        console.log('Waiting for form to load before loading submission data');
        // We'll load the submission data after the form loads in the loadForm method
      }
    }

    // Handle Miniform Data changes
    if (changes['formData'] && changes['formData'].currentValue) {
      this.followUpform = changes['formData'].currentValue;
      this.tempData = JSON.parse(JSON.stringify(this.MainformJson, null, 2));
      console.log(this.tempData,'Main Form JSON' , this.MainformJson);
      this.demo2 = this.followUpform;

      this.buildForm(this.followUpform, this.miniFormGroup);
    }

  }

  ngOnInit() {
    // Check if we're on the form-edit route and handle URL parameters
    this.route.queryParams.subscribe(params => {
      if (params['data']) {
        try {
          // Decode the URL parameter
          const decodedData = decodeURIComponent(atob(params['data']));
          const linkData = JSON.parse(decodedData);

          console.log('Decoded link data:', linkData);

          // Check if this is a new form sharing link
          if (linkData.isNewForm && linkData.formId) {
            console.log('Loading form for editing from shared link:', linkData.formId);
            this.formId = linkData.formId;
            this.isNewFormFromLink = true;
            this.loadForm(linkData.formId);
          }
        } catch (error) {
          console.error('Error decoding form link data:', error);
          this.shareService.showError('Invalid form link. Please check the URL and try again.');
        }
      }
    });

    // Subscribe to the ShareService to handle full window mode requests
    this.subscriptions.push(
      this.shareService.toggleFullWindowMode$.subscribe(() => {
        console.log('Received request to toggle full window mode');
        this.toggleFullWindowMode();
      })
    );
  }

  // toggleHtmlView is now defined below

  loadForm(id: string) {
    // Reset form state before loading new form
    this.resetForm();

    // Form is being loaded, no need to clear image cache anymore
    console.log('Loading form with ID:', id);

    this.coreDataService.getFormByID(id).subscribe({
      next: (data: any) => {
        this.demo2 = data;
        // Build the form with the new template
        this.buildForm(this.demo2);
        // If submissionId is provided, load the submission data after form is built
        if (this.submissionId && this.submissionId !== '') {
          // Add a small delay to ensure the form is fully built
          setTimeout(() => {
            this.loadSubmissionData(this.submissionId);
          }, 100);
        }
      },
      error: (err) => {
        console.error('Error loading form:', err);
      }
    });
  }
  loadSubmissionData(submissionId: string) {
    if (!submissionId || submissionId === '') {
      return;
    }
    this.coreDataService.getFormDatabyid(submissionId).subscribe({
      next: (data: any) => {
        if (data ) {
          setTimeout(() => {
            // First, recreate repeated sections based on saved data
            this.recreateRepeatedSections(data.formData);
            // Then fill the form with the submission data
            setTimeout(() => {
              this.fillFormWithSubmissionData(data);
            }, 100);
          }, 150);
        } else {
          console.warn('Received submission data but formData is missing');
        }
      },
      error: (err) => {
        console.error('Error loading submission data:', err);
      }
    });
  }

  fillFormWithSubmissionData(FormData: FormSubmission) {

    // Fill the form with the submission data
    if (this.formGroup && FormData.formData) {
      Object.keys(FormData.formData).forEach(key => {
        const fieldData = FormData.formData[key];

        // Handle new nested data structure where each field has a 'value' property
        let fieldValue = fieldData;
        let fieldFlag = false;
        let fieldComment = '';
        let fieldCamera = '';
        let fieldFollowUp = null;

        // Check if this is the new nested format
        if (fieldData && typeof fieldData === 'object' && fieldData.hasOwnProperty('value')) {
          const nestedData = fieldData as any;
          fieldValue = nestedData.value;
          fieldFlag = nestedData.flag || false;
          fieldComment = nestedData.comment || '';
          fieldCamera = nestedData.camera || '';
          fieldFollowUp = nestedData.followUp || null;
        }

        // Skip if form control doesn't exist and it's not a clone field
        if (!this.formGroup.controls[key] && !key.includes('_Clone')) {
          return;
        }

        if (this.formGroup.controls[key]) {
          // Set the main field value
          if (this.isDropdownField(key)) {
            this.formGroup.controls[key].setValue(fieldValue, { emitEvent: true });
          } else {
            this.formGroup.controls[key].setValue(fieldValue);
          }

          // Handle flag data - set in selectedFollowUp for UI state
          if (fieldFlag) {
            this.selectedFollowUp[key] = fieldFlag.toString();
          }

          // Handle camera data
          if (fieldCamera && fieldCamera.trim() !== '') {
            const cameraControlName = key + '_image';
            if (this.formGroup.get(cameraControlName)) {
              this.formGroup.get(cameraControlName)?.setValue(fieldCamera);
            }
          }

          // Handle comment data
          if (fieldComment && fieldComment.trim() !== '') {
            const commentControlName = key + '_comment';
            if (this.formGroup.get(commentControlName)) {
              this.formGroup.get(commentControlName)?.setValue(fieldComment);
            }
          }

          // Handle follow-up data
          if (fieldFollowUp) {
            const followUpControlName = key + '_followup';
            if (this.formGroup.get(followUpControlName)) {
              this.formGroup.get(followUpControlName)?.setValue(fieldFollowUp);
            }
          }

          // Handle legacy _flag fields for backward compatibility
          if (key.endsWith('_flag')) {
            const fieldName = key.replace('_flag', '');
            this.selectedFollowUp[fieldName] = fieldValue;
          }
          // Special handling for signature
          const val = FormData.formData[key];
          if (key !== 'managerSignature' && typeof val === 'string' && val.startsWith('data:image/png;base64,')) {
            // console.log('Found signature data in form submission');

            // Store the signature data for later application
            this.pendingSignatureData = val;

            // Set the signature data in the service
            this.signatureService.setPendingSignatureData(val);

            // Ensure the signature pad is initialized
            setTimeout(() => {
              if (this.signaturePadElement) {
                this.signatureService.setSignaturePadElement(this.signaturePadElement);
              }
            }, 500);
          }

          // Special handling for manager signatures
          if (key === 'managerSignatures' && typeof val === 'string') {
            console.log('Found manager signatures data in form submission');

            try {
              this.managerSignatures = JSON.parse(val);
              this.managerSignatureSaved = this.managerSignatures.length > 0;
              console.log('Manager signatures loaded successfully:', this.managerSignatures.length);
            } catch (error) {
              console.error('Error parsing manager signatures:', error);
              this.managerSignatures = [];
              this.managerSignatureSaved = false;
            }
          }
          for (const section of this.demo2.component) {
            for (const component of section.elements) {
              if (component.type === 'file' && component.attributes.field_Id === key) {
                const filePath = FormData.formData[key];
                this.coreDataService.getImage(filePath).subscribe({
                  next: (blob) => {
                    const objectUrl = URL.createObjectURL(blob);
                    this.previewImageUrl = objectUrl;

                    // Also update the component's display properties
                    component.attributes.imageDisplayUrl = objectUrl;
                  },
                  error: (err) => {
                    console.error('Error loading file preview:', err);
                    component.attributes.imageDisplayUrl = 'assets/Images/placeholder-image.png';
                  }
                });
              }
            }
          }
        }
      });
      setTimeout(() => {
        this.formGroup.updateValueAndValidity();
      }, 100);
    }
  }
  isDropdownField(fieldName: string): boolean {
    if (!this.demo2 || !this.demo2.component) return false;

    for (const section of this.demo2.component) {
      for (const component of section.elements) {
        if (component.attributes.field_Id === fieldName &&
            (component.type === 'Select')) {
          return true;
        }
      }
    }
    return false;
  }
  resetForm() {
    // Reset form state
    this.visibleComments = {};
    this.uploadedImages = {};
    this.guidanceImages = {}; // Clear guidance images
    this.pendingSignatureData = null;

    // Clear PDF related data
    this.pdfComponent = '';
    this.pdfPreviewUrls = {};

    // Reset the form group
    if (this.formGroup) {
      this.formGroup = this.fb.group({});
    }

    // Clear signature pad using the service
    this.signatureService.clearSignature();
  }

  ngAfterViewInit() {
    // First, ensure all sections are expanded initially
    // This is important for proper signature pad initialization
    if (this.demo2 && this.demo2.component) {
      this.demo2.component.forEach((section: FormSection) => {
        // Temporarily expand all sections to ensure proper initialization
        if (section.canCollapsed) {
          const hasSignature = section.elements.some(component => component.type === 'signature');
          if (hasSignature) {
            section.isCollapsed = false;
          }
        }
      });
    }

    // Initialize signature pad with a delay to ensure DOM is ready
    setTimeout(() => {
      if (this.signaturePadElement) {

        // Pass the signature pad element to the service
        this.signatureService.setSignaturePadElement(this.signaturePadElement);

        // If we have pending signature data, set it in the service
        if (this.pendingSignatureData) {
          this.signatureService.setPendingSignatureData(this.pendingSignatureData);
        }

        // Set the signature color
        this.signatureService.setSignatureColor(this.signatureColor);
      } else {
      }

      // After signature pad is initialized, collapse sections that should be collapsed
      setTimeout(() => {
        if (this.demo2 && this.demo2.component) {
          this.demo2.component.forEach((section: FormSection) => {
            // Only collapse sections that don't contain signature components
            if (section.canCollapsed && section.isCollapsed) {
              const hasSignature = section.elements.some(component => component.type === 'signature');
              if (!hasSignature) {
                section.isCollapsed = true;
              }
            }
          });
        }
      }, 1000);
    }, 500);

  }

  // Initialize or reinitialize the signature pad
  initializeSignaturePad() {
    // Use the signature service to initialize the pad
    if (this.signaturePadElement) {
      this.signatureService.setSignaturePadElement(this.signaturePadElement);
    }
  }

  // Method removed as it's no longer needed

  // Apply stored signature data to the signature pad if available
  applyStoredSignature() {
    if (this.pendingSignatureData) {
      this.signatureService.setPendingSignatureData(this.pendingSignatureData);
    }
  }

  changePenColor(event: any) {
    this.signatureColor = event.target.value;
    this.signatureService.setSignatureColor(this.signatureColor);
  }

  clearSignature() {
    this.signatureService.clearSignature();
  }

  saveSignature(field: string) {
    if (this.signatureService.isSignatureEmpty()) {
      alert('Please sign before saving.');
      return;
    }

    const signatureDataUrl = this.signatureService.saveSignature();
    if (signatureDataUrl) {
      console.log("Signature Saved");
      this.formGroup.get(field)?.setValue(signatureDataUrl);
      alert('Signature Saved !')
    }
  }

  // Manager signature methods
  initializeManagerSignaturePad() {
    if (this.managerSignaturePadElement) {
      const canvas = this.managerSignaturePadElement.nativeElement;
      console.log('Initializing manager signature pad with canvas:', canvas);
      console.log('Canvas dimensions:', canvas.width, 'x', canvas.height);
      console.log('Canvas client dimensions:', canvas.clientWidth, 'x', canvas.clientHeight);

      // Ensure canvas has proper dimensions
      if (canvas.width === 0 || canvas.height === 0) {
        console.log('Canvas has zero dimensions, setting default size');
        canvas.width = 500;
        canvas.height = 200;
      }

      this.managerSignaturePad = new SignaturePad(canvas, {
        penColor: this.managerSignatureColor,
        backgroundColor: 'white',
        minWidth: 1,
        maxWidth: 3,
        velocityFilterWeight: 0.7
      });

      // Resize the signature pad to fit the canvas
      this.resizeManagerSignaturePad();

      console.log('Manager signature pad initialized successfully');
    } else {
      console.log('Manager signature pad element not found - modal may not be visible');
    }
  }

  // Helper method to resize manager signature pad
  resizeManagerSignaturePad() {
    if (this.managerSignaturePad && this.managerSignaturePadElement) {
      const canvas = this.managerSignaturePadElement.nativeElement;
      const ratio = Math.max(window.devicePixelRatio || 1, 1);

      // Set the actual size in memory (scaled to account for extra pixel density)
      canvas.width = canvas.offsetWidth * ratio;
      canvas.height = canvas.offsetHeight * ratio;

      // Scale the drawing context so everything will work at the higher ratio
      canvas.getContext('2d')?.scale(ratio, ratio);

      // Scale back down using CSS
      canvas.style.width = canvas.offsetWidth + 'px';
      canvas.style.height = canvas.offsetHeight + 'px';

      // Clear the signature pad to ensure it's ready for use
      this.managerSignaturePad.clear();
    }
  }

  changeManagerPenColor(event: any) {
    this.managerSignatureColor = event.target.value;
    if (this.managerSignaturePad) {
      this.managerSignaturePad.penColor = this.managerSignatureColor;
    }
  }

  clearManagerSignature() {
    if (this.managerSignaturePad) {
      this.managerSignaturePad.clear();
      this.managerSignatureSaved = false;
      // Clear the FormGroup control
      this.formGroup.get('managerSignature')?.setValue('');
    }
  }

  // Modal methods
  openManagerSignatureModal() {
    console.log('Opening manager signature modal');
    this.showManagerSignatureModal = true;
    // Initialize signature pad after modal is shown with retry mechanism
    setTimeout(() => {
      this.initializeManagerSignaturePadWithRetry();
    }, 100);
  }

  // Helper method to initialize manager signature pad with retry mechanism
  initializeManagerSignaturePadWithRetry(attempt: number = 1, maxAttempts: number = 5) {
    console.log(`Attempting to initialize manager signature pad - attempt ${attempt}/${maxAttempts}`);

    if (this.managerSignaturePadElement) {
      this.initializeManagerSignaturePad();
    } else if (attempt < maxAttempts) {
      console.log(`Manager signature pad element not found, retrying in ${attempt * 100}ms...`);
      setTimeout(() => {
        this.initializeManagerSignaturePadWithRetry(attempt + 1, maxAttempts);
      }, attempt * 100);
    } else {
      console.error('Failed to initialize manager signature pad after maximum attempts');
      alert('Error initializing signature pad. Please close and reopen the modal.');
    }
  }

  closeManagerSignatureModal() {
    this.showManagerSignatureModal = false;
    this.tempManagerName = '';
    if (this.managerSignaturePad) {
      this.managerSignaturePad.clear();
    }
  }

  // Debug method for manager name input
  onManagerNameChange(event: any) {
    this.tempManagerName = event.target.value;
  }

  saveManagerSignature() {
    if (!this.managerSignaturePad || this.managerSignaturePad.isEmpty()) {
      alert('Please provide manager signature before saving.');
      return;
    }

    if (!this.tempManagerName) {
      alert('Please enter manager name before saving.');
      return;
    }


    const signatureData = this.managerSignaturePad.toDataURL();
    const timestamp = new Date().toLocaleString();
    const signatureId = 'sig_' + Date.now();

    console.log('Signature data generated successfully');

    // Add to signatures array
    const newSignature = {
      id: signatureId,
      name: this.tempManagerName,
      signature: signatureData,
      timestamp: timestamp
    };

    this.managerSignatures.push(newSignature);
    this.managerSignatureSaved = true;

    // Save all signatures to FormGroup as JSON
    this.formGroup.get('managerSignatures')?.setValue(JSON.stringify(this.managerSignatures));

    // Close modal
    this.closeManagerSignatureModal();

    this.onSubmit(); //for form saving !!
  }

  // Remove a specific signature
  removeManagerSignature(signatureId: string) {
    if (confirm('Are you sure you want to remove this signature?')) {
      this.managerSignatures = this.managerSignatures.filter(sig => sig.id !== signatureId);

      // Update FormGroup
      this.formGroup.get('managerSignatures')?.setValue(JSON.stringify(this.managerSignatures));

      // Update saved status
      this.managerSignatureSaved = this.managerSignatures.length > 0;
    }
  }

  signaturePads: { [key: string]: SignaturePad } = {}; // Store signature pads dynamically
  // Formly configuration for the form fields

  fileValidator(validations: any): ValidatorFn {
    return (control: AbstractControl): ValidationErrors | null => {
      const file = control.value;
      if (!file) return null;

      // if (!(file instanceof File)) return { invalidFileType: "Not a valid file" };
      if (typeof file === 'string') return null;
      const fileExt = file.name.split('.').pop()?.toLowerCase();
      const validExtensions = ['jpg', 'jpeg', 'png'];

      if (!fileExt || !validExtensions.includes(fileExt)) {
        console.error("Invalid file extension:", fileExt);
        return { invalidFileType: "Invalid file format! Only JPG, JPEG, and PNG are allowed." };
      }

      if (validations?.maxSize && file.size / 1024 / 1024 > validations.maxSize) {
        return { fileTooLarge: `File size must be less than ${validations.maxSize} MB.` };
      }

      return null;
    };
  }


  dateValidator(validations:any): ValidatorFn {
    return (control: AbstractControl) => {
      if (!control.value) return null;
      const selectedDate = new Date(control.value);
      const today = new Date();

      if (validations.noPast && selectedDate < today) {
        return { pastDate: 'Date cannot be in the past' };
      }

      if (validations.noFuture && selectedDate > today) {
        return { futureDate: 'Date cannot be in the future' };
      }

      if (validations.minDate && selectedDate < new Date(validations.minDate)) {
        return { minDate: `Date must be after ${validations.minDate}` };
      }

      if (validations.maxDate && selectedDate > new Date(validations.maxDate)) {
        return { maxDate: `Date must be before ${validations.maxDate}` };
      }

      return null;
    };
  }

  // QR Scanner properties
  showScanner = false;
  currentScannerComponent: FormComponent | null = null;
  availableFormats = [BarcodeFormat.QR_CODE];

  // HTML view for PDF generation
  showHtmlView: boolean = false;
  isFullWindowMode: boolean = false;
  isNewFormFromLink: boolean = false;



  toggleFullWindowMode() {
    this.isFullWindowMode = !this.isFullWindowMode;
    // Make sure HTML view is enabled when entering full window mode
    if (this.isFullWindowMode) {
      this.showHtmlView = true;
    }
  }




  locationName = '';
  updateLocation(location: { lat: number; lng: number }, field: string) {

    // this.coreDataService.getAddressFromCoords(location.lat, location.lng).subscribe({
    //   next: (data) => {
    //     this.locationName = data.results[0].formatted_address;
    //     console.log('Address:', this.locationName);
    //   },
    //   error: (err) => {
    //     console.error('Error fetching address:', err);
    //   }
    // });

    this.formGroup.get(field)?.setValue(JSON.stringify(location));
    this.formGroup.get(field)?.markAsTouched();
  }

shouldRenderSection(section: FormSection):boolean {
 const group = section.conditionalAtSection;

  if (!group || !group.conditions || group.conditions.length === 0) {
    return true; // No condition = always show
  }

  const logic = group.logic || 'AND'; // default to AND
  const results: boolean[] = [];

  for (const condition of group.conditions) {
    const rawFieldValue = this.formGroup.get(condition.field)?.value;

    const fieldValue = typeof rawFieldValue === 'string'
      ? rawFieldValue.trim().toLowerCase()
      : rawFieldValue;

    const conditionValue = typeof condition.value === 'string'
      ? condition.value.trim().toLowerCase()
      : condition.value;

    let conditionResult = true;
    switch (condition.operator) {
      case '==':
        conditionResult = fieldValue === conditionValue;
        break;
      case '!=':
        conditionResult = fieldValue !== conditionValue;
        break;
      default:
        conditionResult = true; // fallback
    }

    results.push(conditionResult);
  }

  return logic === 'AND'
    ? results.every(result => result)
    : results.some(result => result);
}
shouldRenderField(field: FormComponent) {
  const group = field.conditionalFieldGroup;

  if (!group || !group.conditions|| group.type !== 'visibility' || group.conditions.length === 0) {
    return true; // No condition = always show
  }

  const logic = group.logic || 'AND'; // default to AND
  const results: boolean[] = [];

  for (const condition of group.conditions) {
    const rawFieldValue = this.formGroup.get(condition.field)?.value;

    const fieldValue = typeof rawFieldValue === 'string'
      ? rawFieldValue.trim().toLowerCase()
      : rawFieldValue;

    const conditionValue = typeof condition.value === 'string'
      ? condition.value.trim().toLowerCase()
      : condition.value;

    let conditionResult = true;
    switch (condition.operator) {
      case '==':
        conditionResult = fieldValue === conditionValue;
        break;
      case '!=':
        conditionResult = fieldValue !== conditionValue;
        break;
      default:
        conditionResult = true; // fallback
    }

    results.push(conditionResult);
  }

  return logic === 'AND'
    ? results.every(result => result)
    : results.some(result => result);
}



  buildForm(demo2:FormJson, targetFormGroup: FormGroup = this.formGroup) {
    // this.formGroup = this.fb.group({}); // Reset form group before building
    // First, ensure all sections are expanded initially
    // This is important for proper signature pad initialization
     demo2.component.forEach((section:FormSection) => {
      // Temporarily expand all sections to ensure proper initialization
      if (section.canCollapsed) {
        section.isCollapsed = false;
      }

      section.elements.forEach((component:FormComponent) => {
        let validatorsToAdd: ValidatorFn[] = [];
        const validations = component.attributes.validations || {};

        if (component.attributes.is_required) {
          validatorsToAdd.push(Validators.required);
        }

        // Add minLength & maxLength
        if (validations.minlength) {
          validatorsToAdd.push(Validators.minLength(validations.minlength));
        }
        if (validations.maxlength) {
          validatorsToAdd.push(Validators.maxLength(validations.maxlength));
        }

        // Pattern (skip for file)
        if (validations.pattern && component.type !== 'file') {
          validatorsToAdd.push(Validators.pattern(validations.pattern));
        }

        // Date validations
        if (validations.noFuture) {
          validations.maxDate = new Date().toISOString().split('T')[0];
          console.log(JSON.stringify(validations, null, 2))
        }

        if (validations.noPast) {
          validations.minDate = new Date().toISOString().split('T')[0];
        }

        if (validations.minDate || validations.maxDate) {
          validatorsToAdd.push(this.dateValidator(validations));
        }

        // File validations
        if (component.type === 'file') {
          validatorsToAdd.push(this.fileValidator(validations));
        }

        // Default value
        let defaultValue: any = '';
        if (component.type === 'Select') {
          defaultValue = component.attributes.default_value === 'true' || component.attributes.default_value === 'true';
        } else if (component.attributes.default_value !== undefined) {
          defaultValue = component.attributes.default_value;
        }

        const controlName = component.attributes.field_Id || '';
        if (controlName) {
          targetFormGroup.addControl(controlName, this.fb.control(defaultValue, validatorsToAdd));
        }
        if (component.attributes.actions?.comment) {
          targetFormGroup.addControl(`${component.attributes.field_Id}_comment`, new FormControl(''));
        }
        if (component.attributes.actions?.camera) {
          targetFormGroup.addControl(`${component.attributes.field_Id}_image`, new FormControl(null));
        }
        if (component.attributes.actions?.flag){
           const flagControlName = `${component.attributes.field_Id}_flag`;
          targetFormGroup.addControl(`${component.attributes.field_Id}_flag`, new FormControl(''));
          //  const existingValue = this.formGroup.get(flagControlName)?.value;
          //   if (existingValue) {
          //     this.selectedFollowUp[component.attributes.field_Id] = existingValue;
          //   }
        }
        if (component.attributes.actions?.followupForm?.formId) {
          targetFormGroup.addControl(`${component.attributes.field_Id}_followup`, new FormControl(''));
        }
      });
    });

    // Add manager signatures field to FormGroup
    targetFormGroup.addControl('managerSignatures', new FormControl('[]'));

    // First, ensure all sections with signatures are expanded
    const sectionsWithSignature = this.demo2.component.filter(section =>
      section.elements.some(component => component.type === 'signature')
    );

    if (sectionsWithSignature.length > 0) {
      sectionsWithSignature.forEach(section => {
        if (section.canCollapsed) {
          section.isCollapsed = false;
        }
      });
    }

    // Initialize signature pad after form is built with a longer delay
    setTimeout(() => {
      // Use the signature service to initialize the pad
      if (this.signaturePadElement) {
        this.signatureService.setSignaturePadElement(this.signaturePadElement);

        // If we have pending signature data, set it in the service
        if (this.pendingSignatureData) {
          // console.log('Setting pending signature data from buildForm');
          this.signatureService.setPendingSignatureData(this.pendingSignatureData);
        }
      } else {
        console.log('Signature pad element not found in buildForm');
      }

      // Note: Manager signature pad is initialized only when modal is opened
      // Don't initialize it here as the modal element doesn't exist yet

      // After signature pad is initialized, collapse sections that should be collapsed
      setTimeout(() => {
        this.demo2.component.forEach((section: FormSection) => {
          // Only collapse sections that don't contain signature components
          if (section.canCollapsed && section.isCollapsed) {
            const hasSignature = section.elements.some(component => component.type === 'signature');
            if (!hasSignature) {
              section.isCollapsed = true;
            }
          }
        });
      }, 1000);
    }, 500);

    this.loadImagesForComponents(); //for guide image loading
    this.applyConditionalLogicForRequiredFields();


  }

  // Function which is check condition Type Condiition in Visibility and Required
applyConditionalLogicForRequiredFields() {
  this.demo2.component.forEach((section: FormSection) => {
    section.elements.forEach((comp: FormComponent) => {
      if (!comp.conditionalFieldGroup) return;
      const condGroup = comp.conditionalFieldGroup;
      const fieldId = comp.attributes.field_Id;

      if (condGroup?.type !== 'required' || !fieldId) return;

      const logic = condGroup.logic || 'AND';
      const control = this.formGroup.get(fieldId);
      const update = () => {
        const result = condGroup.conditions.map((c: any) => {
          let rawFieldValue = this.formGroup.get(c.field)?.value;
          const fieldValue = typeof rawFieldValue === 'string' ? rawFieldValue.trim().toLowerCase(): rawFieldValue;
          const FromUserValue = c.value.trim().toLowerCase();
          return c.operator === '==' ? fieldValue === FromUserValue : fieldValue !== FromUserValue;
        });

        const isRequired = logic === 'AND' ? result.every(Boolean) : result.some(Boolean);
        console.log('is required', isRequired);
        if (isRequired) {
          const validator = [Validators.required];
           if (comp.attributes.validations?.pattern) {
                validator.push(Validators.pattern(comp.attributes.validations.pattern));
            }
            control?.setValidators(validator);
        }
        else {
          control?.clearValidators();
        }

        control?.updateValueAndValidity();
      };

      // Attach subscriptions to each dependent field
      condGroup.conditions.forEach((c: any) => {
        this.formGroup.get(c.field)?.valueChanges.subscribe(update);
      });

      update(); // run initially
    });
  });
}

getErrorMessage(field: string, label: string): string {
  const control = this.formGroup.get(field);

  if (control?.hasError('required')) {
    return `${label} is required`;
  }
  if (control?.hasError('pattern')) {
    return `${label} Invalid format`;
  }
  if (control?.hasError('minlength')) {
    return `${label} Must be at least ${control.errors?.['minlength'].requiredLength} characters`;
  }
  if (control?.hasError('maxlength')) {
    return `${label} Cannot exceed ${control.errors?.['maxlength'].requiredLength} characters`;
  }
  if (control?.hasError('pastDate')) return control.errors?.['pastDate'];
  if (control?.hasError('futureDate')) return control.errors?.['futureDate'];
  if (control?.hasError('minDate')) return control.errors?.['minDate'];
  if (control?.hasError('maxDate')) return control.errors?.['maxDate'];
  if (control?.hasError('invalidFileType')) return control.errors?.['invalidFileType'];
  if (control?.hasError('fileTooLarge')) {
    return `This Image is too large ${control.errors?.['fileTooLarge']}`;
  }
  return '';
}
  // Method to recreate repeated sections based on saved form data
  recreateRepeatedSections(formData: any): void {
    if (!formData || !this.demo2?.component) {
      return;
    }

    console.log('Recreating repeated sections from saved data...');

    // Find all field names that have clone patterns
    const clonedFieldNames = Object.keys(formData).filter(key => key.includes('_Clone'));

    if (clonedFieldNames.length === 0) {
      console.log('No repeated sections found in saved data');
      return;
    }

    // Analyze clone patterns to determine how many times each section was repeated
    const sectionRepeatCounts: { [sectionIndex: number]: number } = {};

    // Group cloned fields by section and count repetitions
    for (let sectionIndex = 0; sectionIndex < this.demo2.component.length; sectionIndex++) {
      const section = this.demo2.component[sectionIndex];

      if (!section.repeatable) continue;

      // Find all cloned fields that belong to this section
      const sectionClonedFields = clonedFieldNames.filter(fieldName => {
        const baseFieldName = fieldName.split('_Clone')[0];
        return section.elements.some(element =>
          element.attributes.field_Id === baseFieldName
        );
      });

      if (sectionClonedFields.length === 0) continue;

      // Determine the maximum clone number for this section
      let maxCloneNumber = 0;

      sectionClonedFields.forEach(fieldName => {
        // Extract clone number from field name (e.g., "field_Clone2" -> 2)
        const clonePart = fieldName.split('_Clone')[1];
        if (clonePart === '' || clonePart === undefined) {
          // This is the first clone (no number suffix)
          maxCloneNumber = Math.max(maxCloneNumber, 1);
        } else {
          const cloneNumber = parseInt(clonePart) || 1;
          maxCloneNumber = Math.max(maxCloneNumber, cloneNumber);
        }
      });

      if (maxCloneNumber > 0) {
        sectionRepeatCounts[sectionIndex] = maxCloneNumber;
        console.log(`Section ${sectionIndex} needs ${maxCloneNumber} repetitions`);
      }
    }

    // Create repeated sections in reverse order to maintain correct indices
    const sortedSectionIndices = Object.keys(sectionRepeatCounts)
      .map(key => parseInt(key))
      .sort((a, b) => b - a); // Sort in descending order

    sortedSectionIndices.forEach(sectionIndex => {
      const repeatCount = sectionRepeatCounts[sectionIndex];

      console.log(`Creating ${repeatCount} repeated section(s) for section ${sectionIndex}`);

      // Create the required number of repetitions
      for (let i = 0; i < repeatCount; i++) {
        this.createRepeatedSectionWithNumber(sectionIndex, i + 1);
      }
    });

    // Rebuild the form to include the repeated sections
    this.buildForm(this.demo2);

    // Debug: Log the final form structure
    this.logFormStructure();
  }

  // Debug method to log form structure
  logFormStructure(): void {
    console.log('=== FORM STRUCTURE AFTER RECREATION ===');
    this.demo2.component.forEach((section, index) => {
      console.log(`Section ${index}: "${section.title}" (repeatable: ${section.repeatable})`);
      section.elements.forEach((element, elemIndex) => {
        if (element.attributes.field_Id) {
          console.log(`  Element ${elemIndex}: ${element.attributes.field_Id} (${element.type})`);
        }
      });
    });
    console.log('=== END FORM STRUCTURE ===');
  }

  // Helper method to create a repeated section with a specific clone number
  createRepeatedSectionWithNumber(index: number, cloneNumber: number): void {
    if (!this.demo2?.component || index >= this.demo2.component.length) {
      return;
    }

    const sectionToCopy = this.demo2.component[index];

    // Create a deep copy of the section
    const clonedSection = JSON.parse(JSON.stringify(sectionToCopy));

    // Generate unique field names for the cloned section elements
    clonedSection.elements.forEach((element: FormComponent) => {
      if (element.attributes.field_Id) {
        const originalFieldName = element.attributes.field_Id.split('_Clone')[0]; // Get base name
        element.attributes.field_Id = `${originalFieldName}_Clone${cloneNumber > 1 ? cloneNumber : ''}`;
      }
    });

    // Update section title to indicate it's a clone
    clonedSection.title = `${sectionToCopy.title} (Copy ${cloneNumber})`;

    // Insert the cloned section after the original section
    this.demo2.component.splice(index + cloneNumber, 0, clonedSection);
  }

  // Helper method to create a repeated section (similar to repeatSection but without UI feedback)
  createRepeatedSection(index: number): void {
    if (!this.demo2?.component || index >= this.demo2.component.length) {
      return;
    }

    const sectionToCopy = this.demo2.component[index];

    // Create a deep copy of the section
    const clonedSection = JSON.parse(JSON.stringify(sectionToCopy));

    // Find how many times this section has already been repeated
    let cloneCount = 1;
    const sectionTitle = sectionToCopy.title;

    // Count existing clones
    for (let i = index + 1; i < this.demo2.component.length; i++) {
      const nextSection = this.demo2.component[i];
      const hasMatchingFields = nextSection.elements.some(element => {
        if (!element.attributes.field_Id) return false;
        const fieldName = element.attributes.field_Id;
        return fieldName.includes('_Clone') &&
               sectionToCopy.elements.some(origElement =>
                 origElement.attributes.field_Id &&
                 fieldName.startsWith(origElement.attributes.field_Id.split('_Clone')[0])
               );
      });

      if (hasMatchingFields) {
        cloneCount++;
      } else {
        break;
      }
    }

    // Generate unique field names for the cloned section elements
    clonedSection.elements.forEach((element: FormComponent) => {
      if (element.attributes.field_Id) {
        const originalFieldName = element.attributes.field_Id.split('_Clone')[0]; // Get base name
        element.attributes.field_Id = `${originalFieldName}_Clone${cloneCount > 1 ? cloneCount : ''}`;
      }
    });

    // Update section title to indicate it's a clone
    clonedSection.title = `${sectionTitle} (Copy ${cloneCount})`;

    // Insert the cloned section after the last clone of this section
    const insertIndex = index + cloneCount;
    this.demo2.component.splice(insertIndex, 0, clonedSection);
  }

  repeatSection(index: number): void {
    const sectionToCopy = this.demo2.component[index];

    // Create a deep copy of the section
    const clonedSection = JSON.parse(JSON.stringify(sectionToCopy));

    // Find how many times this section has already been repeated
    let cloneCount = 1;
    const sectionTitle = sectionToCopy.title;

    // Count existing clones by looking at subsequent sections
    for (let i = index + 1; i < this.demo2.component.length; i++) {
      const nextSection = this.demo2.component[i];
      // Check if this is a clone of the same section by comparing field patterns
      const hasMatchingFields = nextSection.elements.some(element => {
        if (!element.attributes.field_Id) return false;
        const fieldName = element.attributes.field_Id;
        return fieldName.includes('_Clone') &&
               sectionToCopy.elements.some(origElement =>
                 origElement.attributes.field_Id &&
                 fieldName.startsWith(origElement.attributes.field_Id.split('_Clone')[0])
               );
      });

      if (hasMatchingFields) {
        cloneCount++;
      } else {
        break; // Stop counting when we reach a different section
      }
    }

    // Generate unique field names for the cloned section elements
    clonedSection.elements.forEach((element: FormComponent) => {
      if (element.attributes.field_Id) {
        const originalFieldName = element.attributes.field_Id.split('_Clone')[0]; // Get base name
        element.attributes.field_Id = `${originalFieldName}_Clone${cloneCount > 1 ? cloneCount : ''}`;
      }
    });

    // Update section title to indicate it's a clone
    clonedSection.title = `${sectionTitle} (${cloneCount})`;

    // Insert the cloned section after the last clone of this section
    const insertIndex = index + cloneCount;
    this.demo2.component.splice(insertIndex, 0, clonedSection);

    // Rebuild the form to include the new section
    this.buildForm(this.demo2);

    // Show success message
    this.shareService.showInfo('Section repeated successfully');
  }

  markFormGroupTouched(formGroup: FormGroup) {

    Object.values(formGroup.controls).forEach(control => {
      control.markAsTouched();
      if (control instanceof FormGroup) {
        this.markFormGroupTouched(control);
      }
    });
  }

  /**
   * Recursively finds the first invalid control and scrolls to it
   */
 scrollToFirstInvalidField() {
  const firstInvalidControl = this.findFirstInvalidControl(this.formGroup);

  if (!firstInvalidControl) return;

  console.log('First invalid control:', firstInvalidControl);

  // Safe and supported selector options
  const selectors = [
    `#${firstInvalidControl}`,
    `[formControlName="${firstInvalidControl}]"`
  ];

  let element: HTMLElement | null = null;

  for (const selector of selectors) {
    // console.log('Trying selector:', selector);
    try {
      const found = document.querySelector(selector) as HTMLElement;
      if (found) {
        element = found;
        console.log('Found element with selector:', selector);
        break;
      }
    } catch (e) {
      continue; // skip invalid selectors
    }
  }

  if (!element) {
    console.warn(`Could not find any element for invalid control: ${firstInvalidControl}`);

    // Try to scroll to the first form group as a fallback
    const firstFormGroup = document.querySelector('.form-group');
    if (firstFormGroup) {
      firstFormGroup.scrollIntoView({ behavior: 'smooth', block: 'center', inline: 'nearest' });
      console.log('Scrolled to first form group as fallback');
    }

    // this.shareService.showError('Please check the form for validation errors and try again.');
    return;
  }

  // Scroll and focus smoothly
  element.scrollIntoView({ behavior: 'smooth', block: 'center', inline: 'nearest' });

  setTimeout(() => {
    if (typeof element.focus === 'function') {
      element.focus();
    }
  }, 300);

  // Friendly error message
  const fieldLabel = this.getFieldLabel(firstInvalidControl) || firstInvalidControl;
  const errorMessage = this.getErrorMessage(firstInvalidControl, fieldLabel);

  this.shareService.showWarning(`Please fix the error in field : ${errorMessage}`);
}


  private findFirstInvalidControl(formGroup: FormGroup): string | null {
    for (const controlName of Object.keys(formGroup.controls)) {
      const control = formGroup.get(controlName);
      if (control?.invalid) {
        if (control instanceof FormGroup) {
          // Recursively check nested FormGroups
          const nestedInvalid = this.findFirstInvalidControl(control);
          if (nestedInvalid) {
            return nestedInvalid;
          }
        } else {
          // Found an invalid control
          return controlName;
        }
      }
    }
    return null;
  }


  toggleSectionCollapse(section: FormSection) {
    if (section.canCollapsed) {
      const wasCollapsed = section.isCollapsed;
      section.isCollapsed = !section.isCollapsed;

      // Check if this section contains a signature component
      const hasSignature = section.elements.some(component => component.type === 'signature');

      // If section was collapsed and is now expanded and contains signature, initialize the signature pad
      if (wasCollapsed && !section.isCollapsed && hasSignature) {
        console.log('Section with signature expanded, initializing signature pad');

        // Use a longer delay to allow the DOM to fully update
        setTimeout(() => {
          // Use the signature service to initialize the pad
          if (this.signaturePadElement) {
            console.log('Signature pad element found after section expansion');
            this.signatureService.setSignaturePadElement(this.signaturePadElement);

            // If we have pending signature data, set it in the service
            if (this.pendingSignatureData) {
              console.log('Setting pending signature data after section expansion');
              this.signatureService.setPendingSignatureData(this.pendingSignatureData);
            }
          } else {
            console.log('Signature pad element not found after section expansion');
          }
        }, 500);
      }
    }
  }

  onSubmit() {
    // Check validation BEFORE emitting data
    if (this.formGroup.invalid) {
      this.markFormGroupTouched(this.formGroup);
      this.scrollToFirstInvalidField();
      return;
    }

    // Only emit data if form is valid
    const data = this.formGroup.getRawValue();
    this.formSubmit.emit(data);

    // Check if manager signature is required and provided (only for new forms)
    // if (!this.submissionId && this.managerSignatures.length === 0) {
    //   alert('At least one manager signature is required before submitting the form. Please click "Add Sign" to add manager approval.');
    //   return;
    // }

    // Create a FormData object for file uploads
    const formData = new FormData();
    Object.keys(this.formGroup.controls).forEach(field => {
      const control = this.formGroup.get(field);
      if (control?.value instanceof File) {
        formData.append(field, control.value);
      } else {
        formData.append(field, control?.value || '');
      }
    });

    // const finalFormData = this.transformFormData();
    // Create the submit data object with form values (manager signature is already in FormGroup)
    const submitData: FormSubmission = {
      id: this.submissionId,
      formTemplateId: this.demo2.id,
      auditHistory: {
        createdBy: this.demo2.auditHistory.userName,
        createdDate: this.demo2.auditHistory.createdDate,
        updatedBy: this.demo2.auditHistory.userName,
        updatedDate: new Date(),
        location: this.demo2.auditHistory.location
      },
      formData: this.formGroup.value
    };

    console.log("Submitting form with manager signature:", JSON.stringify(submitData, null, 2));

    // Send the data to the server
    this.coreDataService.saveFormData(submitData).subscribe({
      next: (_) => {
        // Change sidebar view to 'forms'
        this.shareService.changeSidebarView('forms');
        this.formId = '';
        this.submissionId = '';
        this.resetForm();
        this.router.navigate(['/form-view']);
        // Show success message
        this.shareService.showSuccess('Form submitted successfully !');
      },
      error: (err) => {
        console.error("Error saving form:", err);
        this.shareService.showError('Error submitting form. Please try again.');
      }
    });
    // When User Submit a Form
    this.shareService.BackTOform();
  }


  transformFormData(): any {
    const structuredData: any = {};

    this.demo2.component.forEach(section => {
      section.elements.forEach(element => {
        const fieldName = element.attributes.field_Id;
        if (!fieldName) return; // skip if field_Id is empty

        const fieldValue = this.formGroup.get(fieldName)?.value ?? '';

        // Create a structured object with all possible properties
        const dataEntry: any = {
          value: fieldValue
        };

        // Check if user has flagged this field
        if (this.selectedFollowUp[fieldName]) {
          dataEntry.flag = true;
        }

        // Check for camera/image data
        const cameraFieldName = fieldName + '_image';
        if (this.formGroup.get(cameraFieldName)?.value) {
          dataEntry.camera = this.formGroup.get(cameraFieldName)?.value;
        }

        // Check for comment data
        const commentFieldName = fieldName + '_comment';
        if (this.formGroup.get(commentFieldName)?.value) {
          dataEntry.comment = this.formGroup.get(commentFieldName)?.value;
        }

        // Check for follow-up form data
        const followUpFieldName = fieldName + '_followup';
        if (this.formGroup.get(followUpFieldName)?.value) {
          dataEntry.followUp = this.formGroup.get(followUpFieldName)?.value;
        }

        // Also check element actions for additional metadata
        const actions = element.attributes.actions;
        if (actions) {
          if (actions.flag && !dataEntry.flag) {
            dataEntry.flag = actions.flag;
          }
          if (actions.comment && !dataEntry.comment) {
            dataEntry.comment = actions.comment;
          }
          if (actions.camera && !dataEntry.camera) {
            dataEntry.camera = actions.camera;
          }
        }

        structuredData[fieldName] = dataEntry;
      });
    });

    return structuredData;
  }




    onFileSelect(event: Event, fieldName: string) {
      const input = event.target as HTMLInputElement;
      if (input?.files?.length) {
        const file = input.files[0];

        // Store the current signature data before uploading the image
        const currentSignatureData = this.signatureService.getSignatureData();

        // If we have signature data, store it in the component's pendingSignatureData
        if (currentSignatureData) {
          this.pendingSignatureData = currentSignatureData;
        }

        // Get validations from the JSON configuration
        const fieldConfig = this.demo2.component.flatMap((section: any) => section.elements).find(
          (component: any) => component.attributes.field_Id === fieldName
        );
        const validations = fieldConfig?.attributes.validations;

        // Validate file type
        const allowedExtensions = new RegExp(validations?.pattern || "\\.(jpg|jpeg|png)$", "i");
        if (!allowedExtensions.test(file.name)) {
          this.formGroup.get(fieldName)?.setErrors({ invalidFileType: true });
          return;
        }

        // Validate file size
        const maxSizeMB = validations?.maxSize || 5;
        if (file.size / 1024 / 1024 > maxSizeMB) {
          this.formGroup.get(fieldName)?.setErrors({ fileTooLarge: true });
          return;
        }

        // Preview the uploaded image
        this.previewImageUrl = URL.createObjectURL(file);

        // First, ensure all sections with signatures are expanded
        if (this.demo2 && this.demo2.component) {
          const sectionsWithSignature = this.demo2.component.filter(section =>
            section.elements.some(component => component.type === 'signature')
          );

          if (sectionsWithSignature.length > 0) {
            sectionsWithSignature.forEach(section => {
              if (section.canCollapsed) {
                section.isCollapsed = false;
              }
            });
          }
        }

        this.coreDataService.saveImage(file).subscribe({
          next: (response) => {
            // Store the file path returned from the server
            this.formGroup.get(fieldName)?.setValue(response.imagePath);
            this.formGroup.get(fieldName)?.markAsTouched();

            // Use a series of delayed attempts to reinitialize the signature pad
            // This helps ensure the DOM is fully updated after the API call
            const attemptSignatureInit = (attempt = 1, maxAttempts = 3) => {

              // Use the signature service to initialize the pad
              if (this.signaturePadElement) {
                // console.log('Signature pad element found, initializing');
                this.signatureService.setSignaturePadElement(this.signaturePadElement);

                // If we have pending signature data, set it in the service
                if (this.pendingSignatureData || currentSignatureData) {
                  const dataToRestore = this.pendingSignatureData || currentSignatureData;
                  // console.log('Setting signature data after image upload');
                  this.signatureService.setPendingSignatureData(dataToRestore);

                  // Also update the form control
                  if (this.formGroup.get('signature')) {
                    this.formGroup.get('signature')?.setValue(dataToRestore);
                  }
                }

                // Check if the signature was successfully applied
                setTimeout(() => {
                  if (this.signatureService.isSignatureEmpty() && this.pendingSignatureData && attempt < maxAttempts) {
                    attemptSignatureInit(attempt + 1, maxAttempts);
                  }
                }, 300);
              } else if (attempt < maxAttempts) {
                setTimeout(() => attemptSignatureInit(attempt + 1, maxAttempts), 250);
              } else {
                console.log('Failed to find signature pad element after multiple attempts');
              }
            };

            // Start the first attempt after a delay to allow DOM updates
            setTimeout(() => attemptSignatureInit(), 600);
          },
          error: (err) => {
            console.error('Error saving image:', err);
            this.formGroup.get(fieldName)?.setErrors({ uploadFailed: true });

            // Even on error, try to restore the signature data
            setTimeout(() => {
              if (this.signaturePadElement && (currentSignatureData || this.pendingSignatureData)) {
                const dataToRestore = currentSignatureData || this.pendingSignatureData;
                console.log('Restoring signature data after image upload error');
                this.signatureService.setSignaturePadElement(this.signaturePadElement);
                this.signatureService.setPendingSignatureData(dataToRestore);
              }
            }, 500);
          }
        });
      }
    }

    loadImagesForComponents(): void {
      for (const section of this.demo2.component) {
        for (const component of section.elements) {
          if (component.type === 'image' && component.attributes?.image_url) {
            // Store the guidance image path with a key based on the field name or label
            const imageKey = component.attributes.field_Id || component.attributes.label || `image_${Math.random().toString(36).substring(2, 9)}`;
            this.guidanceImages[imageKey] = component.attributes.image_url;

            this.coreDataService.getImage(component.attributes.image_url).subscribe({
              next: (blob) => {
                const objectUrl = URL.createObjectURL(blob);
                component.attributes.imageDisplayUrl = objectUrl;
              },
              error: () => {
                component.attributes.imageDisplayUrl = 'assets/Images/placeholder-image.png';
              }
            });
          }
        }
      }
    }

    onComment(component: any) {
      const key = `${component.attributes.field_Id}_comment`;
      this.visibleComments[key] = !this.visibleComments[key];
    }
    onCamera(component: any) {
      const inputId = 'file_' + component.attributes.field_Id;
      const fileInput = document.getElementById(inputId) as HTMLInputElement;
      fileInput?.click();
    }
    onFileSelected(event: Event, component: any) {
      const file = (event.target as HTMLInputElement)?.files?.[0];
      if (file) {
        // Store the current signature data before uploading the image
        const currentSignatureData = this.signatureService.getSignatureData();

        // If we have signature data, store it in the component's pendingSignatureData
        if (currentSignatureData) {
          this.pendingSignatureData = currentSignatureData;
        }

        const controlName = `${component.attributes.field_Id}_image`;
        component.attributes.uploadedFileName = file.name;

        // Call the saveImage API to save the image to the server
        this.coreDataService.saveImage(file).subscribe({
          next: (response) => {
            console.log('Image saved successfully:', response);
            // Store the server response (image path) in the form control
            this.formGroup.get(controlName)?.setValue(response.imagePath || response);

            // Also store the image URL for display
            component.attributes.image_url = URL.createObjectURL(file);
          },
          error: (error) => {
            console.error('Error saving image:', error);
            this.shareService.showError('Failed to save image. Please try again.');
            // Still set the file for local preview even if server save fails
            this.formGroup.get(controlName)?.setValue(file);
            component.attributes.image_url = URL.createObjectURL(file);
          }
        });

        // First, ensure all sections with signatures are expanded
        if (this.demo2 && this.demo2.component) {
          const sectionsWithSignature = this.demo2.component.filter(section =>
            section.elements.some(component => component.type === 'signature')
          );

          if (sectionsWithSignature.length > 0) {
            console.log(`Found ${sectionsWithSignature.length} sections with signature components`);
            sectionsWithSignature.forEach(section => {
              if (section.canCollapsed) {
                console.log('Ensuring signature section is expanded before file selection');
                section.isCollapsed = false;
              }
            });
          }
        }

        // Use a series of delayed attempts to reinitialize the signature pad
        const attemptSignatureInit = (attempt = 1, maxAttempts = 3) => {
          // Use the signature service to initialize the pad
          if (this.signaturePadElement) {
            // console.log('Signature pad element found, initializing');
            this.signatureService.setSignaturePadElement(this.signaturePadElement);

            // If we have pending signature data, set it in the service
            if (this.pendingSignatureData || currentSignatureData) {
              const dataToRestore = this.pendingSignatureData || currentSignatureData;
              // console.log('Setting signature data after file selection');
              this.signatureService.setPendingSignatureData(dataToRestore);

              // Also update the form control
              if (this.formGroup.get('signature')) {
                this.formGroup.get('signature')?.setValue(dataToRestore);
              }
            }

            // Check if the signature was successfully applied
            setTimeout(() => {
              if (this.signatureService.isSignatureEmpty() && this.pendingSignatureData && attempt < maxAttempts) {
                console.log('Signature not applied successfully, trying again');
                attemptSignatureInit(attempt + 1, maxAttempts);
              }
            }, 300);
          } else if (attempt < maxAttempts) {
            console.log('Signature pad element not found, will try again');
            setTimeout(() => attemptSignatureInit(attempt + 1, maxAttempts), 150);
          } else {
            console.log('Failed to find signature pad element after multiple attempts');
          }
        };

        // Start the first attempt after a delay to allow DOM updates
        setTimeout(() => attemptSignatureInit(), 600);
      }
    }

    FollowUp: string[] = [
    'Corrective Action',
    'Review Later',
    'Report',
    'Escalate',
  ];

    selectItem(item: string, component: any) {
    this.selectedFollowUp[component.attributes.field_Id] = item;
    const key = `${component.attributes.field_Id}_flag`;
    this.visibleflag[key] =  !this.visibleflag[key];
      if (this.formGroup.contains(key)) {
        this.formGroup.get(key)?.setValue(item);
      }
    }
    onFlag(component: any) {
      const key = `${component.attributes.field_Id}_flag`;
      this.visibleflag[key] =  !this.visibleflag[key];
    }
// FOllowUp forms renderd*******************
    activeMiniFormElement: FormComponent | null = null;
    miniFormData!: FormJson;
    miniFormGroup: FormGroup;
    showMiniFormModal: boolean = false;
onFollowup(component: FormComponent) {
    const formId = component.attributes.actions?.followupForm?.formId;
    if (!formId) return;

    this.activeMiniFormElement = component;

      this.coreDataService.getFormByID(formId).subscribe({
        next: (data) => {
          this.miniFormData = data; // Form structure
          this.miniFormGroup = this.fb.group({}); // Reset mini form group
          // this.buildForm(this.miniFormData, this.miniFormGroup);
          this.showMiniFormModal = true;
        },
        error: () => {
          this.shareService.showError("Failed to load follow-up form.");
        }
    });
}
submitMiniForm(): void {
  if (this.miniFormRef) {
    // Check if mini form is valid before submitting
    if (this.miniFormRef.formGroup.invalid) {
      this.miniFormRef.markFormGroupTouched(this.miniFormRef.formGroup);
      this.miniFormRef.scrollToFirstInvalidField();
      return;
    }

    const data = this.miniFormRef.getFormData(); // expose a method from <app-home>
    this.saveMiniFormData(data);
  }
}
getFormData(): any {
  return this.miniFormGroup.getRawValue();
}

cancelMiniForm(): void {

  this.miniFormRef?.resetForm();
  this.demo2 = this.tempData;
if (this.demo2?.component) {
  this.buildForm(this.demo2, this.formGroup);
} else {
  console.error('Main form JSON is invalid or missing component array:', this.demo2);
}
  this.MainformJson = null;
  this.showMiniFormModal = false;
  this.activeMiniFormElement = null;
}
saveMiniFormData(data: any): void {
  if (!this.miniFormGroup || !this.activeMiniFormElement) return;

    const fieldName = this.activeMiniFormElement.attributes.field_Id;
    const followUpControlName = `${fieldName}_followup`;

    // Save the follow-up form data
    if (!this.formGroup.contains(followUpControlName)) {
      this.formGroup.addControl(followUpControlName, new FormControl(null));
    }
    this.formGroup.get(followUpControlName)?.setValue(data);
    //rebuild the main form
    this.demo2 = this.tempData;
    if (this.demo2?.component) {
      this.buildForm(this.demo2, this.formGroup);
    } else {
      console.error('Main form JSON is invalid or missing component array:', this.demo2);
    }

    this.showMiniFormModal = false;
    this.activeMiniFormElement = null;
    this.miniFormGroup.reset();
    // this.miniFormData = null;
    this.shareService.showSuccess('Follow-up form saved.');

}

     // Helper method to determine if a field is an image field
     isImageField(key: string, value: string): boolean {
      // Check if the value is a file path
      if (value.startsWith('C:') || value.includes('/uploads/') || value.includes('\\uploads\\')) {
        return true;
      }

      // Check if the field name suggests it's an image
      if (key.toLowerCase().includes('image') || key.toLowerCase().includes('photo') || key.toLowerCase().includes('picture')) {
        return true;
      }

      // Check if the value is a file path with image extension
      if (value.includes('.jpg') || value.includes('.jpeg') || value.includes('.png') || value.includes('.gif')) {
        return true;
      }

      // Check if it's a file field in the form structure
      for (const section of this.demo2.component) {
        for (const component of section.elements) {
          if (component.type === 'file' && component.attributes.field_Id === key) {
            return true;
          }
        }
      }
      return false;
    }

  // PDF
  pdfComponent: string = '';
  pdfPreviewUrls: { [fieldName: string]: string } = {}; // Store PDF preview URLs for each field

  onPdfSelect(event: Event, fieldName: string){
      const input = event.target as HTMLInputElement;

  if (input.files && input.files.length > 0) {
    const file = input.files[0];

    // Validate file type
    if (file.type !== 'application/pdf') {
      this.shareService.showError('Please select a valid PDF file');
      return;
    }

    // Validate file size (e.g., max 10MB)
    const maxSizeMB = 10;
    if (file.size / 1024 / 1024 > maxSizeMB) {
      this.shareService.showError(`File size must be less than ${maxSizeMB}MB`);
      return;
    }
    // Also create a preview URL for display purposes
    const reader = new FileReader();
    reader.onload = () => {
      const base64String = (reader.result as string);
      this.pdfComponent = base64String;

      // Save base64 string in form control so it can be stored in MongoDB
      this.formGroup.get(fieldName)?.setValue(base64String);
      this.formGroup.get(fieldName)?.markAsTouched();

      // Store preview URL for the specific field
      this.setPdfPreviewUrl(fieldName, base64String);
    };

    reader.readAsDataURL(file); // This reads the PDF as base64 string for preview
  }
  }

  // Set PDF preview URL for a specific field
  setPdfPreviewUrl(fieldName: string, base64String: string): void {
    this.pdfPreviewUrls[fieldName] = base64String;
  }

  // Get PDF preview URL for a specific field
  getPdfPreviewUrl(fieldName: string): string | null {
    return this.pdfPreviewUrls[fieldName] || null;
  }

  // Check if a field has a PDF file
  hasPdfFile(fieldName: string): boolean {
    const control = this.formGroup.get(fieldName);
    const value = control?.value;
    return typeof value === 'string' && value.startsWith('data:application/pdf;base64,');
  }

  // Open PDF from form control (blob)
  openPdfFromFormControl(fieldName: string): void {
    const control = this.formGroup.get(fieldName);
    const value = control?.value;
    if (!value) return;
    // If it's a File object
    if (value instanceof File) {
      const blob = new Blob([value], { type: 'application/pdf' });
      const blobUrl = URL.createObjectURL(blob);
      window.open(blobUrl, '_blank');
    }// If it's a base64 string
  else if (typeof value === 'string' && value.startsWith('data:application/pdf;base64,')) {
    const blob = this.base64ToBlob(value, 'application/pdf');
    const blobUrl = URL.createObjectURL(blob);
    window.open(blobUrl, '_blank');
  }
  }
  base64ToBlob(base64: string, contentType: string): Blob {
  const byteCharacters = atob(base64.split(',')[1]);
  const byteNumbers = new Array(byteCharacters.length).fill(0).map((_, i) => byteCharacters.charCodeAt(i));
  const byteArray = new Uint8Array(byteNumbers);
  return new Blob([byteArray], { type: contentType });
}

  // Open PDF from base64 string (for backward compatibility)
  openPdfFromBase64(base64String: string): void {
    const byteCharacters = atob(base64String.split(',')[1] || base64String);
    const byteNumbers = new Array(byteCharacters.length).fill(0).map((_, i) => byteCharacters.charCodeAt(i));
    const byteArray = new Uint8Array(byteNumbers);
    const blob = new Blob([byteArray], { type: 'application/pdf' });

    const blobUrl = URL.createObjectURL(blob);
    window.open(blobUrl, '_blank');
  }


    // Helper method to get a user-friendly label for a field
    getFieldLabel(fieldName: string): string | null {
      if (!this.demo2 || !this.demo2.component) {
        return null;
      }

      // Search through all form components to find a matching field name
      for (const section of this.demo2.component) {
        for (const component of section.elements) {
          if (component.attributes.field_Id === fieldName) {
            return component.attributes.label || null;
          }
        }
      }

      return null;
    }


    showTimePicker(input: HTMLInputElement): void {
      input.focus(); // This will trigger the time picker on most browsers
    }
    // QR Scanner methods
    startScan(component: FormComponent) {
      this.showScanner = true;
      this.currentScannerComponent = component;
    }

    stopScan() {
      this.showScanner = false;
      this.currentScannerComponent = null;
    }

    handleScanSuccess(result: string, component: FormComponent) {
      console.log('QR code scanned successfully:', result);
      const fieldName = component.attributes.field_Id || '';
      if (fieldName && this.formGroup.get(fieldName)) {
        this.formGroup.get(fieldName)?.setValue(result);
        this.formGroup.get(fieldName)?.markAsTouched();
        this.stopScan();
      }
    }

    handleScanError(error: any) {
      console.error('QR scan error:', error);
    }

    handleScanComplete(_: any) {
      // Optional: Handle scan complete event
    }

    // OneDrive popup properties
    showOneDriveDialog = false;
    isLoadingFolders = false;
    oneDriveError = '';
    isUploading = false;
    uploadProgress = 0;
    pdfFile: File | null = null;
    folders: OneDriveFolder[] = [];
    displayedFolders: OneDriveFolder[] = [];
    selectedFolderId: string = '';
    currentFolderId: string = 'Home';
    folderPath: OneDriveFolder[] = [];
    showingNewFolderInput: boolean = false;
    newFolderName: string = '';

    // Subscription to manage OneDrive service observables
    private oneDriveSubscriptions: Subscription[] = [];

    /**
     * Setup subscriptions to OneDrive service observables
     */
    setupOneDriveSubscriptions() {
      // Clear any existing subscriptions
      this.oneDriveSubscriptions.forEach(sub => sub.unsubscribe());
      this.oneDriveSubscriptions = [];

      // Subscribe to loading state
      this.oneDriveSubscriptions.push(
        this.oneDriveService.loading$.subscribe(isLoading => {
          this.isLoadingFolders = isLoading;

          // If loading has completed, check if we have folders
          if (!isLoading && this.folders.length === 0) {
            console.log('Loading completed but no folders found');
          }
        })
      );

      // Subscribe to error messages
      this.oneDriveSubscriptions.push(
        this.oneDriveService.error$.subscribe(error => {
          if (error) {
            console.log('OneDrive error:', error);
            this.oneDriveError = error;
          } else {
            this.oneDriveError = '';
          }
        })
      );

      // Subscribe to folders
      this.oneDriveSubscriptions.push(
        this.oneDriveService.folders$.subscribe(folders => {
          console.log(`Received ${folders.length} folders from OneDrive service`);
          this.folders = folders;

          if (folders.length === 0 && !this.isLoadingFolders) {
            console.log('No folders found in your OneDrive');
          }
        })
      );

      // Subscribe to upload progress
      this.oneDriveSubscriptions.push(
        this.oneDriveService.uploadProgress$.subscribe(progress => {
          this.isUploading = progress.isLoading;
          this.uploadProgress = progress.progress;

          if (progress.error) {
            this.shareService.showError(progress.error);
          }

          // If upload is complete (100%) and no error, close the dialog after a delay
          if (progress.progress === 100 && !progress.error) {
            setTimeout(() => {
              this.hideOneDrivePopup();
              this.shareService.showSuccess('File uploaded successfully to OneDrive');
            }, 500);
          }
        })
      );
    }

    showOneDrivePopup() {

      // Store the current signature data before generating the PDF
      const currentSignatureData = this.signatureService.getSignatureData();

      if (!this.formHtmlView) {
        this.shareService.showError('Form HTML view not available');
        return;
      }

      this.formHtmlView.generatePdf(true) // Use the form-html-view component's generatePdf method
        .then((pdfBytes: Uint8Array | void) => {
          if (!pdfBytes) {
            this.shareService.showError('Failed to generate PDF for OneDrive upload');
            return;
          }

          try {
            // Get the form name and sanitize it for the file name
            let formName = this.demo2.auditHistory.formName || 'form';

            // Remove leading/trailing spaces and invalid characters
            formName = formName.trim();

            // Replace invalid characters with underscores
            formName = formName.replace(/[\\/:*?"<>|]/g, '_');

            // Ensure the name is not empty
            if (formName.length === 0) {
              formName = 'form';
            }

            const fileName = `${formName}.pdf`;

            // Create a new Uint8Array to ensure we're working with a clean copy
            const bytes = new Uint8Array(pdfBytes);
            const blob = new Blob([bytes], { type: 'application/pdf' });
            this.pdfFile = new File([blob], fileName, {
              type: 'application/pdf'
            });
            // Show the OneDrive popup
            this.showOneDriveDialog = true;

            // Load OneDrive folders
            this.loadOneDriveFolders();

            // Setup subscriptions to OneDrive service observables
            this.setupOneDriveSubscriptions();


            // Ensure the signature data is preserved
            setTimeout(() => {
              if (this.signaturePadElement) {
                this.signatureService.setSignaturePadElement(this.signaturePadElement);

                if (currentSignatureData || this.pendingSignatureData) {
                  const dataToRestore = currentSignatureData || this.pendingSignatureData;
                  this.signatureService.setPendingSignatureData(dataToRestore);
                }
              }
            }, 250);
          } catch (error) {
            console.error('Error creating file for OneDrive:', error);
            // Even on error, try to restore the signature data
            setTimeout(() => {
              if (this.signaturePadElement && (currentSignatureData || this.pendingSignatureData)) {
                const dataToRestore = currentSignatureData || this.pendingSignatureData;
                this.signatureService.setSignaturePadElement(this.signaturePadElement);
                this.signatureService.setPendingSignatureData(dataToRestore);
              }
            }, 500);
          }
        })
        .catch(error => {
          console.error('Error generating PDF for OneDrive:', error);
          this.shareService.showError('Error generating PDF: ' + (error as Error).message);
        });
    }

    hideOneDrivePopup() {
      this.showOneDriveDialog = false;
      this.selectedFolderId = '';
      this.oneDriveError = '';
      this.isUploading = false;
      this.uploadProgress = 0;
      this.currentFolderId = 'Home';
      this.folderPath = [];
      this.showingNewFolderInput = false;
      this.newFolderName = '';

      // Reset folder expansion state
      this.folders.forEach(folder => {
        folder.isExpanded = false;
      });
    }

    /**
     * Switch Microsoft account for OneDrive access
     */
    switchAccount() {
      // Store the current active account ID to compare later
      const currentAccountId = this.msalService.instance.getActiveAccount()?.homeAccountId;

      // Show loading indicator
      this.isLoadingFolders = true;
      this.oneDriveError = '';

      // Force account selection by passing true to login method
      this.oneDriveService.login(true).subscribe({
        next: (loginResponse: any) => {
          // Check if the user selected the same account
          const selectedAccountId = loginResponse.account.homeAccountId;
          const isSameAccount = currentAccountId === selectedAccountId;

          console.log('Account switch:', isSameAccount ? 'Same account selected' : 'New account selected');

          // Set the selected account as active
          this.msalService.instance.setActiveAccount(loginResponse.account);

          // Clear folders and selected folder



          this.folders = [];
          this.selectedFolderId = '';

          // Reload folders with the selected account
          this.oneDriveService.loadOneDriveFolders().subscribe({
            next: (_) => {
              // This will be handled by the subscription in setupOneDriveSubscriptions
              if (isSameAccount) {
                console.log('Refreshed folders for the same account');
              } else {
                console.log('Loaded folders for new account');
              }
            },
            error: (error) => {
              console.error('Error loading folders after account switch:', error);
              this.shareService.showError('Failed to load folders after account switch');
              this.isLoadingFolders = false; // Ensure loading indicator is hidden on error
            }
          });
        },
        error: (error: any) => {
          this.isLoadingFolders = false;
          this.oneDriveError = 'Account selection failed: ' + (error.message || 'Unknown error');
        }
      });
    }

    loadOneDriveFolders() {
      // Reset folder path when loading root folders
      if (this.currentFolderId === 'Home') {
        this.folderPath = [];
      }

      // Use the OneDriveService to load folders
      // The service will handle authentication and account selection
      this.oneDriveService.loadOneDriveFolders().subscribe({
        next: (folders) => {
          this.folders = folders;
          this.displayedFolders = folders;

          if (folders.length === 0) {
            this.shareService.showWarning('No folders found in your OneDrive');
          }
        },
        error: (error) => {
          console.error('Error loading folders:', error);
          this.shareService.showError('Failed to load OneDrive folders');
        }
      });

      // Setup subscriptions to track loading state and errors
      // These are already set up in setupOneDriveSubscriptions()
    }

    /**
     * Load subfolders of a specific folder
     * @param folder The folder to load subfolders for
     */
    loadSubfolders(folder: OneDriveFolder) {
      if (!folder || folder.isLoading) {
        return;
      }

      // Mark the folder as loading
      folder.isLoading = true;

      this.oneDriveService.loadSubfolders(folder.id).subscribe({
        next: (subfolders) => {
          folder.children = subfolders;
          folder.isLoading = false;
          folder.isExpanded = true;


        },
        error: (error) => {
          folder.isLoading = false;
          console.error('Error loading subfolders:', error);
          this.shareService.showError(`Failed to load subfolders for "${folder.name}"`);
        }
      });
    }



    /**
     * Navigate to a specific folder
     * @param folder The folder to navigate to
     */
    navigateToFolder(folder: OneDriveFolder) {
      // Set loading state
      folder.isLoading = true;

      this.currentFolderId = folder.id;

      // Add the folder to the path if it's not already there
      const existingIndex = this.folderPath.findIndex(f => f.id === folder.id);
      if (existingIndex === -1) {
        this.folderPath.push(folder);
      } else {
        // If the folder is already in the path, truncate the path to that point
        this.folderPath = this.folderPath.slice(0, existingIndex + 1);
      }

      // Load the subfolders of this folder
      this.oneDriveService.loadSubfolders(folder.id).subscribe({
        next: (subfolders) => {
          this.displayedFolders = subfolders;
          folder.isLoading = false;

          // If no subfolders were found, show a message
          if (subfolders.length === 0) {
            this.shareService.showInfo(`No subfolders found in "${folder.name}"`);
          }
        },
        error: (error) => {
          folder.isLoading = false;
          console.error('Error loading subfolders:', error);
          this.shareService.showError(`Failed to load subfolders for "${folder.name}"`);
        }
      });
    }

    /**
     * Navigate to the root folder
     */
    navigateToRoot() {
      this.currentFolderId = 'Home';
      this.folderPath = [];
      this.displayedFolders = this.folders;
    }

    /**
     * Navigate to a folder in the path
     * @param index The index of the folder in the path
     */
    navigateToPathItem(index: number) {
      if (index < 0 || index >= this.folderPath.length) {
        return;
      }

      const folder = this.folderPath[index];
      folder.isLoading = true;
      this.currentFolderId = folder.id;

      // Truncate the path to this point
      this.folderPath = this.folderPath.slice(0, index + 1);

      // If it's the last item in the path, we're already there
      if (index === this.folderPath.length - 1) {
        folder.isLoading = false;
        return;
      }

      // Load the subfolders of this folder
      this.oneDriveService.loadSubfolders(folder.id).subscribe({
        next: (subfolders) => {
          this.displayedFolders = subfolders;
          folder.isLoading = false;

          // If no subfolders were found, show a message
          if (subfolders.length === 0) {
            this.shareService.showInfo(`No subfolders found in "${folder.name}"`);
          }
        },
        error: (error) => {
          folder.isLoading = false;
          console.error('Error loading subfolders:', error);
          this.shareService.showError(`Failed to load subfolders for "${folder.name}"`);
        }
      });
    }

    /**
     * Show the new folder input
     */
    showNewFolderInput() {
      this.showingNewFolderInput = true;
      this.newFolderName = '';

      // Focus the input after it's shown
      setTimeout(() => {
        const input = document.querySelector('.new-folder-input input') as HTMLInputElement;
        if (input) {
          input.focus();
        }
      }, 100);
    }

    /**
     * Get the name of the current folder
     * @returns The name of the current folder
     */
    getCurrentFolderName(): string {
      if (this.currentFolderId === 'Home') {
        return 'Home';
      }

      // If we have a folder path, return the name of the last folder in the path
      if (this.folderPath.length > 0) {
        return this.folderPath[this.folderPath.length - 1].name;
      }

      // If we have a selected folder, return its name
      if (this.selectedFolderId) {
        const selectedFolder = this.findFolderById(this.selectedFolderId);
        if (selectedFolder) {
          return selectedFolder.name;
        }
      }

      return 'Unknown Folder';
    }

    /**
     * Find a folder by its ID
     * @param folderId The ID of the folder to find
     * @returns The folder with the specified ID, or undefined if not found
     */
    findFolderById(folderId: string): OneDriveFolder | undefined {
      // First, check the root folders
      const rootFolder = this.folders.find(f => f.id === folderId);
      if (rootFolder) {
        return rootFolder;
      }

      // If not found in root, recursively search through all folders
      return this.findFolderInChildren(this.folders, folderId);
    }

    /**
     * Recursively search for a folder in a list of folders and their children
     * @param folders The list of folders to search
     * @param folderId The ID of the folder to find
     * @returns The folder with the specified ID, or undefined if not found
     */
    findFolderInChildren(folders: OneDriveFolder[], folderId: string): OneDriveFolder | undefined {
      for (const folder of folders) {
        if (folder.id === folderId) {
          return folder;
        }

        if (folder.children && folder.children.length > 0) {
          const found = this.findFolderInChildren(folder.children, folderId);
          if (found) {
            return found;
          }
        }
      }

      return undefined;
    }

    /**
     * Cancel creating a new folder
     */
    cancelNewFolder() {
      this.showingNewFolderInput = false;
      this.newFolderName = '';
    }

    /**
     * Create a new folder
     */
    createNewFolder() {
      if (!this.newFolderName.trim()) {
        this.shareService.showWarning('Please enter a folder name');
        return;
      }

      // Create the folder in the current location
      const parentId = this.currentFolderId;

      this.oneDriveService.createFolder(parentId, this.newFolderName.trim())
        .then(newFolder => {
          this.shareService.showSuccess(`Folder "${newFolder.name}" created successfully`);

          // Hide the new folder input
          this.showingNewFolderInput = false;
          this.newFolderName = '';

          // If we're in the root, refresh the root folders
          if (parentId === 'Home') {
            this.loadOneDriveFolders();
          } else {
            // If we're in a subfolder, refresh the current folder's contents
            const currentFolder = this.folderPath[this.folderPath.length - 1];
            this.navigateToFolder(currentFolder);
          }
        })
        .catch(error => {
          console.error('Error creating folder:', error);
          this.shareService.showError(`Failed to create folder: ${error.message || 'Unknown error'}`);
        });
    }

    /**
     * Select a folder for upload
     * @param folder The folder to select
     */
    selectFolder(folder: OneDriveFolder) {
      if (!folder) {
        console.error('Attempted to select a null or undefined folder');
        return;
      }

      console.log('Selected folder:', folder.name, 'with ID:', folder.id);
      this.selectedFolderId = folder.id;

      // Log the selected folder details for debugging
      if (folder.id === 'Home') {
        console.warn('Home is not a valid folder ID for upload. Please select a specific folder.');
        this.shareService.showWarning('Please select a specific folder, not the root.');
      }
    }

    /**
     * @returns The name of the selected folder
     */
    getSelectedFolderName(): string {
      if (!this.selectedFolderId) {
        return '';
      }

      // First check in the current displayed folders (which could be subfolders)
      const displayedFolder = this.displayedFolders.find(f => f.id === this.selectedFolderId);
      if (displayedFolder) {
        return displayedFolder.name;
      }

      // Then check in the current folder's children if we're in a subfolder
      if (this.folderPath.length > 0) {
        const currentFolder = this.folderPath[this.folderPath.length - 1];
        if (currentFolder.children) {
          const subfolder = currentFolder.children.find(f => f.id === this.selectedFolderId);
          if (subfolder) {
            return subfolder.name;
          }
        }
      }

      // Finally, use the recursive search through all folders
      const selectedFolder = this.findFolderById(this.selectedFolderId);
      if (selectedFolder) {
        return selectedFolder.name;
      }

      // If we still can't find it, log a warning and return a default value
      console.warn(`Could not find folder with ID: ${this.selectedFolderId}`);
      return 'Selected Folder';
    }

    uploadToSelectedFolder() {
      if (!this.pdfFile) {
        this.shareService.showError('No file to upload');
        return;
      }

      // Use the selected folder ID or the current folder ID if no folder is selected
      const targetFolderId = this.selectedFolderId || this.currentFolderId;

      if (!targetFolderId) {

        return;
      }

      // console.log('Uploading file to folder ID:', targetFolderId);

      // Show uploading state
      this.isUploading = true;
      this.uploadProgress = 10;

      // Use the OneDriveService to upload the file
      // The service will handle authentication, account selection, and upload progress
      this.oneDriveService.uploadToSelectedFolder(this.pdfFile, targetFolderId)
        .then(webUrl => {
          // Hide the popup after successful upload
          setTimeout(() => {
            this.hideOneDrivePopup();
            this.shareService.showSuccess('File uploaded successfully to OneDrive');

            // Open the file in a new tab if webUrl is available
            if (webUrl) {
              window.open(webUrl, '_blank');
            }
          }, 500);
        })
        .catch(error => {
          console.error('Error uploading file:', error);

          // Extract the most useful error message
          let errorMessage = 'Unknown error';
          this.shareService.showError('Upload failed: ' + errorMessage);
          this.isUploading = false;
          this.uploadProgress = 0;
          // Error handling is done through the uploadProgress$ observable
        });
    }

    arrayBufferToBase64(buffer: Uint8Array): Promise<string> {
      return new Promise((resolve, reject) => {
        try {
          // Create a new Uint8Array to ensure we're working with a clean copy
          const bytes = new Uint8Array(buffer);
          const blob = new Blob([bytes], { type: 'application/pdf' });
          const reader = new FileReader();
          reader.onload = () => {
            if (reader.result) {
              const base64 = (reader.result as string).split(',')[1];
              resolve(base64);
            } else {
              reject(new Error('Failed to read file data'));
            }
          };
          reader.onerror = (error) => reject(error);
          reader.readAsDataURL(blob);
        } catch (error) {
          console.error('Error in arrayBufferToBase64:', error);
          reject(error);
        }
      });
    }

    scopes = ['Mail.ReadWrite', 'Mail.Send', 'Files.ReadWrite', 'User.Read'];
    async sendEmailWithOutlookDraft() {
      // Store the current signature data before generating the PDF
      // We store this to restore it later, even though we don't use it directly
      this.pendingSignatureData = this.signatureService.getSignatureData();
      try {

        const activeAccount = this.msalService.instance.getActiveAccount();

        if (!activeAccount) {
          this.msalService.loginRedirect({ scopes: this.scopes });
          return;
        }

        const tokenResult = await this.msalService.instance.acquireTokenSilent({
          account: activeAccount,
          scopes: this.scopes
        });

        const accessToken = tokenResult.accessToken;
        const headers = new HttpHeaders({
          Authorization: `Bearer ${accessToken}`,
          'Content-Type': 'application/json'
        });

        // 1. Generate the PDF
        if (!this.formHtmlView) {
          this.shareService.showError('Form HTML view not available');
          return;
        }

        const pdfBytes = await this.formHtmlView.generatePdf(true);
        if (!pdfBytes) {
          console.error('PDF generation failed.');
          this.shareService.showError('Failed to generate PDF for email');
          return;
        }

        // Get the form name and sanitize it for the file name
        let formName = this.demo2.auditHistory.formName || 'form';

        // Remove leading/trailing spaces and invalid characters
        formName = formName.trim();

        // Replace invalid characters with underscores
        formName = formName.replace(/[\\/:*?"<>|]/g, '_');

        // Ensure the name is not empty
        if (formName.length === 0) {
          formName = 'form';
        }

        const fileName = `${formName}.pdf`;
        console.log('Creating email attachment with name:', fileName);

        const base64Pdf = await this.arrayBufferToBase64(pdfBytes);

        // 2. Create the draft with attachment
        const draftPayload = {
          subject: `${this.demo2.auditHistory.formName || 'Form'} Document`,
          body: {
            contentType: 'Text',
            content: 'Please find the attached document.'
          },
          toRecipients: [],
          attachments: [
            {
              "@odata.type": "#microsoft.graph.fileAttachment",
              name: fileName,
              contentType: "application/pdf",
              contentBytes: base64Pdf
            }
          ]
        };

        const draftResponse: any = await firstValueFrom(
          this.http.post('https://graph.microsoft.com/v1.0/me/messages', draftPayload, { headers })
        );
        const draftWebLink = draftResponse.webLink;
        console.log('Draft created with ID:', draftResponse);

        if (this.isMobileDevice()) {

          const outlookAppLink = 'ms-outlook://';
          window.location.href = outlookAppLink;

          // Optionally, show a toast:
          setTimeout(() => {
            this.shareService.showInfo('Outlook app opened. Please check your Drafts folder.');
          }, 500);

        } else {
          // Desktop: Open draft in Outlook Web (read-only, due to Microsoft limitation)
          window.open(draftWebLink, "_blank");
        }

      } catch (error) {
        console.error('Error sending email with draft:', error);
        this.shareService.showError('Error preparing email: ' + (error as Error).message);
      }
    }

    isMobileDevice(): boolean {
      return /Mobi|Android|iPhone|iPad/i.test(navigator.userAgent);
    }

    generateHTMLViewShareableLink() {

      if (!this.submissionId) {
        this.shareService.showWarning('Please save the form first to generate a shareable link.');
        return;
      }

      try {

        const linkData = {
          formId: this.formId,
          submissionId: this.submissionId,
          timestamp: new Date().getTime(),
          directHtmlView: true // Flag to indicate direct HTML view
        };

        // Convert to JSON and encode with Base64
        const jsonData = JSON.stringify(linkData);
        const encodedData = btoa(encodeURIComponent(jsonData));


        const baseUrl = window.location.origin;
        const shareableLink = `${baseUrl}/form-preview?data=${encodedData}`;

        // Copy to clipboard
        navigator.clipboard.writeText(shareableLink)
          .then(() => {
            this.shareService.showSuccess('Secure shareable link copied to clipboard!');
          })
          .catch(err => {
            console.error('Could not copy link: ', err);
            this.shareService.showError('Failed to copy link to clipboard.');
          });
      } catch (error) {
        console.error('Error generating encoded link:', error);
        this.shareService.showError('Failed to generate shareable link.');
      }
    }

}


