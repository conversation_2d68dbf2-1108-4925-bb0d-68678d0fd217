import { Component } from '@angular/core';
import { CoreDataService } from '../../core-data.service';
import { column, db, SpParameter } from '../Models/Comany-Model';
import { forkJoin } from 'rxjs';
import { orderBy, process, SortDescriptor, State } from '@progress/kendo-data-query';
import { DataStateChangeEvent, GridDataResult } from '@progress/kendo-angular-grid';
import { Router } from '@angular/router';
import { ShareService } from '../../SharedData/share-services.service';

@Component({
  selector: 'app-data-source',
  templateUrl: './../Template/data-source.component.html',
  styleUrl: './../Style/data-source.component.css'
})
export class DataSourceComponent  {
  databases: db[] = []; //DB Names

  dbString: string = ''; // connection Name refernce

  storedProcedureList: string[] = []; //SP List for DropDown

  storedProcedure: string = ''; //selected SP

  spParameters: SpParameter[] = []; //parameter for Sp's

  spInputValues: any = {}; //Input values in Sp's

  dataSourceList: column[] = []; // All Data Source
  Filteredlist: column[] = [];

  showModal: boolean = false; // for Open PopUPModel

  public state: State = { //For Grid Size and paging
    skip: 0,
    take: 40,
    sort: [],
  };
  gridHeight: number = 0;
  screenSize: number = 0;
  public gridData: GridDataResult = process(this.dataSourceList, this.state);

  // For Preview Data in view for User
  previewData: any[] = [];
  spResultColumns: string[] = [];

  columnMapping: column = { // for saving Data in our MongoDB
  id: '',
  auditHistory: {
    dataSource: '', // use some input or prompt
    spName: '',
    dbKey: '',
    savedBy: 'Eudemonic', // get from auth/user context
    savedAt: new Date()
  },
  columns: []
};
constructor(private coreData:CoreDataService, private router:Router, private shareService:ShareService){}

  ngOnInit(): void {
     this.screenSize = window.innerHeight;
    this.gridHeight = this.screenSize - 100;
    this.loadInitialData();
  }

public dataStateChange(state: DataStateChangeEvent): void {
  this.state = state;
  this.gridData = process(this.Filteredlist, this.state);
}
public sortChange(sort: SortDescriptor[]): void {
  this.gridData = process(orderBy(this.Filteredlist, sort), this.state);
  }


// This is For a Edit of Data Source On cell double Click
onCellClick(e:any){

  let rowIndex;
  rowIndex = e.target.parentElement.rowIndex;
  let rowData = this.gridData.data[rowIndex];
  this.coreData.GetDataSourceByID(rowData.id).subscribe({
    next:(data)=>{
      this.columnMapping = data;
      // this.columnMapping.id = data.id;
      this.dbString = data.auditHistory.dbKey;
      this.storedProcedure = data.auditHistory.spName;
      this.ExecuteSP();
      this.showModal = true; // for Opning a PopoUp Model
    },
    error:(err)=>{
      console.log(err);
    }
  })

}

  loadInitialData(): void {
    forkJoin({
      databases: this.coreData.GetDatabases(),
      dataSources: this.coreData.GetDataSource()
    }).subscribe({
    next: ({ databases, dataSources }) => {
      this.databases = databases; //shift DB name for dropDownList
        const locationList = dataSources.map((item: any) => {
        return {
          id: item.id,
          auditHistory: item.auditHistory,
        };
      }); //Shift DataSource Name here
      this.dataSourceList = locationList;
      this.Filteredlist = locationList;

      this.gridData = {
      data: this.dataSourceList.slice(this.state.skip, this.state.take),
      total: this.dataSourceList.length,
    };
      console.log(this.gridData, 'gridData');
    },
    error: (err) => {
      console.error('Error loading initial data:', err);
    }
  });
}

  GetConnectionString(DB: any):void{
    console.log(DB, 'DB');
    this.coreData.GetConnectionString(DB).subscribe({
      next:(data)=>{
        this.storedProcedureList=data;
        console.log(this.storedProcedureList, 'storedProcedureList');
      },
      error:(err)=>{
        console.log(err);
      }
    })
  }


  GetSPparameter(SPparameter: string){
    this.coreData.GetParametersForSP(this.dbString, SPparameter).subscribe({
      next:(data)=>{
        console.log(data, 'SPparameter');
        this.spParameters = data;
      },
      error:(err)=>{
        console.log(err);
      }
    })
  }

  // helper method
getInputType(sqlType: string): string {
switch (sqlType.toLowerCase()) {
  case 'int': return 'number';
  case 'bit': return 'checkbox';
  case 'datetime': return 'datetime-local';
  default: return 'text';
}
}
isNumeric(type: string): boolean {
  return ['int', 'bigint', 'decimal', 'float', 'double', 'numeric', 'smallint'].includes(type.toLowerCase());
}
isDate(type: string): boolean {
  return ['date', 'datetime', 'smalldatetime', 'datetime2'].includes(type.toLowerCase());
}
isBoolean(type: string): boolean {
  return ['bit', 'boolean'].includes(type.toLowerCase());
}
isText(type: string): boolean {
  return ['varchar', 'nvarchar', 'text', 'char', 'nchar'].includes(type.toLowerCase());
}


ExecuteSP(): void {
  this.coreData.ExecuteSP(this.dbString, this.storedProcedure, this.spInputValues).subscribe({
    next: (data) => {
      this.spInputValues={};
       if (data.length > 0) {

        this.previewData = (data || []).slice(0, 10);  //shift Data for preview
         this.spResultColumns = Object.keys(data[0]);

        const firstRow = data[0];

        const extractedColumns = Object.keys(firstRow).map(key => {
          return {
            name: key,
            DataType: typeof firstRow[key] // optional - just a rough JS type
          };
        });

        this.columnMapping.auditHistory.spName = this.storedProcedure;
        this.columnMapping.auditHistory.dbKey = this.dbString;
        this.columnMapping.columns = extractedColumns;
        // Here Save this Data In mongoDB which is Our DataBase //For Saving DataSource in Db MongoDB
        console.log(this.columnMapping, 'columnMapping');

      }
    },
    error: (err) => {
      console.log(err);
    }
  });
}
// Here Save this Data In mongoDB which is Our DataBase
saveColumnMapping():void{ //the fucntion which is reason to so save Data in MongoDB
    this.coreData.saveSPData(this.columnMapping).subscribe({
    next: (response) => {
      console.log('Column mapping saved successfully:', response);
      this.shareService.showSuccess('DataSource Created Successfully...')
      this.showModal = false;
      this.loadInitialData();
    },
    error: (err) => {
      console.error('Error saving column mapping:', err);
    }
  });
}

}
