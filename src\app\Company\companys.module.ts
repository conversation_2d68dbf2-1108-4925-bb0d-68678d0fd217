import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

import { CompanysRoutingModule } from './companys-routing.module';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { CompanyIntregationComponent } from './Component/company-intregation.component';
import { DataSourceComponent } from './Component/data-source.component';
import { DropDownsModule } from '@progress/kendo-angular-dropdowns';
import { DialogModule } from '@progress/kendo-angular-dialog';
import { LabelModule } from '@progress/kendo-angular-label';
import { InputsModule, TextBoxModule } from '@progress/kendo-angular-inputs';
import { NumericTextBoxModule } from '@progress/kendo-angular-inputs';
import { DateInputsModule } from '@progress/kendo-angular-dateinputs';
import { ButtonModule } from '@progress/kendo-angular-buttons';
import { GridModule } from '@progress/kendo-angular-grid';


@NgModule({
  declarations: [
    CompanyIntregationComponent,
    DataSourceComponent,
  ],
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    CompanysRoutingModule,
    DialogModule,
    DropDownsModule,
    LabelModule,
    TextBoxModule,
    NumericTextBoxModule,
    DateInputsModule,
    ButtonModule,
    InputsModule,
    GridModule,
  ]
})
export class CompanysModule { }
