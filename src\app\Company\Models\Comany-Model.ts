export interface company{
  businessName:string,
  businessRegistrationNumber:string,
  EmailID:string,
  phoneNumber:string,
  country:string,
  state:string,
  city:string,
  address:string,
  postalCode:number
}

export interface SpParameter{
  PARAMETER_NAME:string,
  DATA_TYPE:string
}

export interface db{
  name:string,
  connectionStringName:string
}

export interface column{
  id?: string,
  auditHistory: {
    dataSource: string,
    spName: string,
    dbKey: string,
    savedBy: string,
    savedAt: Date
  },
  columns: {
    Name: string,
    DataType: string
  }[]
}
