<div class="company-settings-container">
  <!-- Sidebar -->
  <div class="sidebar1">
    <div class="sidebar-header1">
      <div class="header-icon1">
        <i class="fas fa-cog"></i>
      </div>
      <h3>Company Settings</h3>
    </div>
    <div class="sidebar-content1">
      <nav class="sidebar-nav1">
        <ul class="nav-list1">
          <li class="nav-item1">
            <a class="nav-link1"
               (click)="selectSection('company')"
               [ngClass]="{ 'active': selectedSection === 'company' }">
              <div class="nav-icon1">
                <i class="far fa-building"></i>
              </div>
              <span class="nav-text1">Company Info</span>
              <div class="nav-indicator1" *ngIf="selectedSection === 'company'"></div>
            </a>
          </li>
          <li class="nav-item1">
            <a class="nav-link1"
               (click)="selectSection('integration')"
               [ngClass]="{ 'active': selectedSection === 'integration' }">
              <div class="nav-icon1">
                <i class="fas fa-plug"></i>
              </div>
              <span class="nav-text1">Integration</span>
              <div class="nav-indicator1" *ngIf="selectedSection === 'integration'"></div>
            </a>
          </li>
           <li class="nav-item1">
            <a class="nav-link1"
               (click)="selectSection('data-source')"
               [ngClass]="{ 'active': selectedSection === 'data-source' }">
              <div class="nav-icon1">
                <i class="fa-solid fa-database"></i>
              </div>
              <span class="nav-text1">Data Connections</span>
              <div class="nav-indicator1" *ngIf="selectedSection === 'data-source'"></div>
            </a>
          </li>
        </ul>
      </nav>
    </div>
  </div>
  <!-- Main Content Area -->
  <div class="main-content1">
    <!-- Company Info Section -->
    <ng-container *ngIf="selectedSection === 'company'">
      <div class="content-section1">
        <!-- <div class="section-header1">
          <div class="section-title1">
            <i class="far fa-building"></i>
            <h2>Company Information</h2>
          </div>
          <p class="section-description1">Manage your company details and settings</p>
        </div> -->
        <div class="content-card1">
          <div class="card-content1">
            <!-- Company Reactive Form -->
            <form [formGroup]="companyForm" (ngSubmit)="onCompanySubmit()">

              <!-- Business Info Section -->
              <div class="form-section">
                <div class="section-header">
                  <i class="fas fa-building section-icon"></i>
                  <h3 class="section-title">Business Information</h3>
                </div>

                <div class="form-grid">
                  <!-- Business Name -->
                  <div class="form-field full-width">
                    <label class="field-label">Business Name</label>
                    <input
                      type="text"
                      formControlName="businessName"
                      placeholder="Enter business name"
                      class="field-input"
                      [class.error]="isFieldInvalid('businessName')">
                    <div class="error-message" *ngIf="isFieldInvalid('businessName')">
                      {{ getFieldErrorMessage('businessName') }}
                    </div>
                  </div>

                  <!-- Business Registration Number -->
                  <div class="form-field">
                    <label class="field-label">Registration Number</label>
                    <input
                      type="text"
                      formControlName="businessRegistrationNumber"
                      placeholder="Business registration number"
                      class="field-input"
                      [class.error]="isFieldInvalid('businessRegistrationNumber')">
                    <div class="error-message" *ngIf="isFieldInvalid('businessRegistrationNumber')">
                      {{ getFieldErrorMessage('businessRegistrationNumber') }}
                    </div>
                  </div>

                  <!-- Email -->
                  <div class="form-field">
                    <label class="field-label">Email Address</label>
                    <input
                      type="email"
                      formControlName="EmailID"
                      placeholder="<EMAIL>"
                      class="field-input"
                      [class.error]="isFieldInvalid('EmailID')">
                    <div class="error-message" *ngIf="isFieldInvalid('EmailID')">
                      {{ getFieldErrorMessage('EmailID') }}
                    </div>
                  </div>

                  <!-- Phone Number -->
                  <div class="form-field">
                    <label class="field-label">Phone Number</label>
                    <input
                      type="text"
                      formControlName="phoneNumber"
                      placeholder="+1234567890"
                      class="field-input"
                      [class.error]="isFieldInvalid('phoneNumber')">
                    <div class="error-message" *ngIf="isFieldInvalid('phoneNumber')">
                      {{ getFieldErrorMessage('phoneNumber') }}
                    </div>
                  </div>
                </div>
              </div>

              <!-- Address Section -->
              <div class="form-section">
                <div class="section-header">
                  <i class="fas fa-map-marker-alt section-icon"></i>
                  <h3 class="section-title">Address Information</h3>
                </div>

                <div class="form-grid">
                  <!-- Country -->
                  <div class="form-field">
                    <label class="field-label">Country</label>
                    <input
                      type="text"
                      formControlName="country"
                      placeholder="Country"
                      class="field-input"
                      [class.error]="isFieldInvalid('country')">
                    <div class="error-message" *ngIf="isFieldInvalid('country')">
                      {{ getFieldErrorMessage('country') }}
                    </div>
                  </div>

                  <!-- State -->
                  <div class="form-field">
                    <label class="field-label">State/Province</label>
                    <input
                      type="text"
                      formControlName="state"
                      placeholder="State or Province"
                      class="field-input"
                      [class.error]="isFieldInvalid('state')">
                    <div class="error-message" *ngIf="isFieldInvalid('state')">
                      {{ getFieldErrorMessage('state') }}
                    </div>
                  </div>

                  <!-- City -->
                  <div class="form-field">
                    <label class="field-label">City</label>
                    <input
                      type="text"
                      formControlName="city"
                      placeholder="City"
                      class="field-input"
                      [class.error]="isFieldInvalid('city')">
                    <div class="error-message" *ngIf="isFieldInvalid('city')">
                      {{ getFieldErrorMessage('city') }}
                    </div>
                  </div>

                  <!-- Postal Code -->
                  <div class="form-field">
                    <label class="field-label">Postal Code</label>
                    <input
                      type="number"
                      formControlName="postalCode"
                      placeholder="Postal Code"
                      class="field-input"
                      [class.error]="isFieldInvalid('postalCode')">
                    <div class="error-message" *ngIf="isFieldInvalid('postalCode')">
                      {{ getFieldErrorMessage('postalCode') }}
                    </div>
                  </div>

                  <!-- Street Address -->
                  <div class="form-field full-width">
                    <label class="field-label">Street Address</label>
                    <input
                      type="text"
                      formControlName="address"
                      placeholder="Street address"
                      class="field-input"
                      [class.error]="isFieldInvalid('address')">
                    <div class="error-message" *ngIf="isFieldInvalid('address')">
                      {{ getFieldErrorMessage('address') }}
                    </div>
                  </div>
                </div>
              </div>

              <!-- Form Actions -->
              <div class="form-actions" *ngIf="isFormDirty">
                <button type="submit" class="save-button">Save Company Information</button>
                <button type="button" class="cancel-button" (click)="onCompanyCancel()">Cancel</button>
              </div>
            </form>
          </div>
        </div>
      </div>
    </ng-container>

    <!-- Integration Section -->
    <ng-container *ngIf="selectedSection === 'integration'">
      <div class="content-section">
        <!-- <div class="section-header">
          <div class="section-title">
            <i class="fas fa-plug"></i>
            <h2>Microsoft Integration</h2>
          </div>
          <p class="section-description">Configure Microsoft Azure AD integration settings</p>
        </div> -->

        <div class="integration-card">
          <div class="card-header">
            <div class="card-title">
              <div class="microsoft-logo">
                <i class="fab fa-microsoft"></i>
              </div>
              <div class="title-text">
                <h3>Microsoft Azure AD</h3>
                <p>Connect your application with Microsoft services</p>
              </div>
            </div>
            <button class="edit-button" (click)="isEditing = !isEditing">
              <i class="fas" [ngClass]="isEditing ? 'fa-times' : 'fa-edit'"></i>
              {{ isEditing ? 'Cancel' : 'Edit' }}
            </button>
          </div>

          <div class="card-content">
            <!-- Edit Mode -->
            <form *ngIf="isEditing" (ngSubmit)="save()" class="integration-form">
              <div class="form-grid-intrigration">
                <div class="form-group">
                  <label class="form-label">
                    <i class="fas fa-key"></i>
                    Client ID
                  </label>
                  <input
                    [(ngModel)]="clientId"
                    name="clientId"
                    type="text"
                    class="form-input"
                    placeholder="Enter your Azure AD Client ID"
                    required />
                  <small class="form-hint">Your Azure AD application's Client ID</small>
                </div>

                <div class="form-group">
                  <label class="form-label">
                    <i class="fas fa-building"></i>
                    Tenant ID
                  </label>
                  <input
                    [(ngModel)]="tenantId"
                    name="tenantId"
                    type="text"
                    class="form-input"
                    placeholder="Enter your Azure AD Tenant ID"
                    required />
                  <small class="form-hint">Your Azure AD tenant's unique identifier</small>
                </div>
              </div>

              <div class="form-actions">
                <button type="submit" class="save-button">
                  <i class="fas fa-save"></i>
                  Save Configuration
                </button>
              </div>
            </form>

            <!-- View Mode -->
            <div *ngIf="!isEditing" class="integration-display">
              <div class="info-grid">
                <div class="info-item">
                  <div class="info-label">
                    <i class="fas fa-key"></i>
                    Client ID
                  </div>
                  <div class="info-value">
                    <span class="value-text">{{ clientId || 'Not configured' }}</span>
                    <button class="copy-button" (click)="copy(clientId)" *ngIf="clientId">
                      <i class="fas fa-copy"></i>
                      Copy
                    </button>
                  </div>
                </div>

                <div class="info-item">
                  <div class="info-label">
                    <i class="fas fa-building"></i>
                    Tenant ID
                  </div>
                  <div class="info-value">
                    <span class="value-text">{{ tenantId || 'Not configured' }}</span>
                    <button class="copy-button" (click)="copy(tenantId)" *ngIf="tenantId">
                      <i class="fas fa-copy"></i>
                      Copy
                    </button>
                  </div>
                </div>
              </div>

              <div class="status-indicator" *ngIf="clientId && tenantId">
                <div class="status-badge connected">
                  <i class="fas fa-check-circle"></i>
                  Configuration Complete
                </div>
              </div>

              <div class="status-indicator" *ngIf="!clientId || !tenantId">
                <div class="status-badge disconnected">
                  <i class="fas fa-exclamation-triangle"></i>
                  Configuration Required
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </ng-container>

    <ng-container *ngIf="selectedSection === 'data-source'">
       <div class="content-section1">
        <!-- <div class="content-card1"> -->
          <app-data-source></app-data-source>
        <!-- </div> -->
        </div>
    </ng-container>


  </div>
</div>
