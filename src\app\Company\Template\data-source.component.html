<div class="data-source-container">

  <kendo-grid
  [data]="gridData"
  [height]="200"
  filterable="menu"
  adaptiveMode="auto"
  [pageSize]="state.take"
  [skip]="state.skip"
  [sort]="state.sort"
  [height]="gridHeight"
  [pageable]="true"
  [filter]="state.filter"
  [sortable]="{allowUnsort: true,mode:'multiple'}"
  (dataStateChange)="dataStateChange($event)"
  (sortChange)="sortChange($event)"
  (dblclick)="onCellClick($event)"
  [scrollable]="'scrollable'">

   <ng-template kendoGridToolbarTemplate position="top">
     <kendo-grid-spacer></kendo-grid-spacer>
     <button kendoButton themeColor="primary" (click)="showModal = true">Create Data Source</button>
    </ng-template>

   <kendo-grid-column field="auditHistory.dataSource" title="DataSource Name" [width]="150">
   </kendo-grid-column>
   <kendo-grid-column field="auditHistory.spName" title="SP Name" [width]="200">
   </kendo-grid-column>
   <kendo-grid-column field="auditHistory.savedAt" title="Saved Time" [width]="200">
   </kendo-grid-column>
   <kendo-grid-column field="auditHistory.savedBy" title="End Date" [width]="200">
   </kendo-grid-column>
   </kendo-grid>








<!-- (click)="ExecuteSP()" -->



  <!-- Enhanced Modal for enter DataSource name -->
<div class="modal-backdrop" *ngIf="showModal">
  <div class="modal-content">

    <!-- Modal Header -->
    <h3>Data Source Configure</h3>

    <!-- Modal Body - Scrollable Content -->
    <div class="modal-body">
      <div class="creationDataSource">
    <input
      type="text"
      [(ngModel)]="columnMapping.auditHistory.dataSource"
      placeholder=" Data Source Name"
      class="modal-input"
    />

    <!-- create a new data Source -->
    <!-- <kendo-label> -->
        <kendo-dropdownlist class="modal-input"
          [data]="databases"
          [(ngModel)] = dbString
          [textField]="'name'"
          [valueField]="'connectionStringName'"
          [defaultItem]="{ name: 'Select DB', connectionStringName: '' }"
          (valueChange)="GetConnectionString($event)"
          [valuePrimitive]="true">
        </kendo-dropdownlist>
    <!-- </kendo-label> -->


    <!-- <kendo-label> -->
        <kendo-dropdownlist class="modal-input"
          [data]="storedProcedureList"
          [(ngModel)] = storedProcedure

          [defaultItem]="'Select SP'"
          (valueChange)="GetSPparameter(storedProcedure)"
          [valuePrimitive]="true">
        </kendo-dropdownlist>
    <!-- </kendo-label> -->

    <button kendoButton themeColor="primary" (click)="ExecuteSP()" >Execute Data</button>
  </div>


  <div *ngIf="(spParameters?.length || 0) > 0">
    <h1>SP Parameter here</h1>
    <div *ngFor="let param of spParameters" class="SpParameters">
      <div class="SpParameters-input">
        <label>{{ param.PARAMETER_NAME }}</label>
        <input style="padding: 4px 3px;"
          class="modal-input"
          [(ngModel)]="spInputValues[param.PARAMETER_NAME]"
          [type]="getInputType(param.DATA_TYPE)"
          [placeholder]="param.DATA_TYPE"/>
      </div>
    </div>
  </div>



<div *ngIf="previewData.length > 0;">
  <p *ngIf="previewData.length > 10">
    Showing only first 10 rows (of {{ previewData.length }})
  </p>
  <div class="table-container">
  <table class="data-table">
    <thead>
      <tr>
        <th *ngFor="let col of spResultColumns">{{ col }}</th>
      </tr>
    </thead>
    <tbody>
      <tr *ngFor="let row of previewData | slice:0:10">
        <td *ngFor="let col of spResultColumns">{{ row[col] }}</td>
      </tr>
    </tbody>
  </table>
</div>
</div>









      </div> <!-- End of modal-body -->

      <!-- Modal Actions - Always Visible at Bottom -->
      <div class="modal-actions">
        <button (click)="saveColumnMapping()">Save</button>
        <button class="cancel" (click)="showModal = false">Cancel</button>
      </div>

  </div> <!-- End of modal-content -->
</div> <!-- End of modal-backdrop -->


</div>


