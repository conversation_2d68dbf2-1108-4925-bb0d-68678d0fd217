.form-html-view-container {
  width: 100%;
  max-width: 800px;
  margin: 0 auto;
  padding: 20px;
  background-color: #f9f9f9;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.actions-bar {
  display: flex;
  justify-content: flex-end;
  margin-bottom: 20px;
}

.action-button {
  background-color: #2196F3;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 10px 15px;
  font-size: 14px;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 8px;
  transition: background-color 0.3s;
}

.action-button:hover {
  background-color: #0b7dda;
}

.action-button i {
  font-size: 16px;
}

.form-content {
  background-color: white;
  padding: 30px;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

/* Header styles */
.form-header {
  display: flex;
  align-items: center;
  margin-bottom: 30px;
  padding-bottom: 20px;
  border-bottom: 1px solid #eee;
}

.logo-container {
  flex: 0 0 150px;
  margin-right: 20px;
}

.header-logo {
  max-width: 100%;
  height: auto;
}

.header-text {
  flex: 1;
}

.form-title {
  margin: 0 0 5px 0;
  font-size: 24px;
  color: #333;
}

.form-subtitle {
  margin: 0;
  font-size: 14px;
  color: #666;
}

/* Section styles */
.form-section {
  margin-bottom: 30px;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  border: 1px solid #e8e8e8;
}

.no-sections-message {
  background-color: #f9f9f9;
  padding: 24px;
  text-align: center;
  border-radius: 8px;
  border: 1px solid #e0e0e0;
  margin-bottom: 24px;
}

.no-sections-message p {
  color: #666;
  font-style: italic;
  margin: 0;
  font-size: 15px;
}

.section-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 16px 20px;
  border-radius: 8px 8px 0 0;
  position: relative;
}

.section-header::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
}

.section-header h3 {
  margin: 0;
  font-size: 18px;
  color: white;
  font-weight: 600;
  position: relative;
  z-index: 1;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

/* Table styles */
.form-table {
  width: 100%;
  border-collapse: collapse;
  margin-bottom: 0;
  background-color: white;
  border: 1px solid #e0e0e0;
  border-top: none;
  border-radius: 0 0 8px 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.form-table th,
.form-table td {
  padding: 16px 20px;
  text-align: left;
  border-bottom: 1px solid #f0f0f0;
  vertical-align: top;
}

.form-table tr:last-child td {
  border-bottom: none;
}

.form-table tr:hover {
  background-color: #fafafa;
  transition: background-color 0.2s ease;
}

.form-table th {
  background-color: #f8f9fa;
  font-weight: 600;
  color: #333;
  font-size: 14px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.field-column {
  width: 35%;
  min-width: 200px;
}

.value-column {
  width: 65%;
}

.field-name {
  font-weight: 600;
  color: #444;
  vertical-align: top;
  font-size: 14px;
  line-height: 1.4;
  padding-right: 20px;
}

.field-value {
  color: #333;
  word-break: break-word;
  font-size: 14px;
  line-height: 1.5;
  min-height: 20px;
  display: flex;
  align-items: flex-start;
}

/* Image row styles */
.image-row td {
  padding-top: 20px;
  padding-bottom: 20px;
}

.form-image,
.signature-image {
  max-width: 100%;
  max-height: 250px;
  border: 2px solid #e0e0e0;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.form-image:hover,
.signature-image:hover {
  transform: scale(1.02);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

.signature-image {
  max-height: 120px;
  background-color: #fafafa;
}

.no-image,
.no-value {
  color: #999;
  font-style: italic;
  padding: 8px 12px;
  background-color: #f8f9fa;
  border-radius: 4px;
  border: 1px dashed #ddd;
}

/* Textarea content */
.textarea-content {
  white-space: pre-wrap;
  background-color: #f8f9fa;
  padding: 16px;
  border-radius: 6px;
  border: 1px solid #e0e0e0;
  min-height: 80px;
  max-height: 250px;
  overflow-y: auto;
  font-size: 14px;
  line-height: 1.5;
  color: #333;
  box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.05);
}

/* Object content */
.object-content {
  white-space: pre-wrap;
  background-color: #f8f9fa;
  padding: 16px;
  border-radius: 6px;
  border: 1px solid #e0e0e0;
  font-family: 'Courier New', monospace;
  font-size: 13px;
  max-height: 250px;
  overflow-y: auto;
  margin: 0;
  color: #444;
  box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.05);
}

/* Signature section */
.signature-section {
  margin-top: 30px;
  padding-top: 20px;
  border-top: 1px solid #eee;
}

/* Field type specific styles */
.field-value ul {
  margin: 0;
  padding-left: 24px;
  background-color: #f8f9fa;
  border-radius: 6px;
  padding: 12px 12px 12px 32px;
  border-left: 3px solid #007bff;
}

.field-value li {
  margin-bottom: 6px;
  font-size: 14px;
  line-height: 1.4;
}

.field-value li:last-child {
  margin-bottom: 0;
}

/* Boolean values */
.boolean-value {
  display: inline-flex;
  align-items: center;
  font-weight: 600;
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 13px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.boolean-value.true {
  color: #2e7d32;
  background-color: #e8f5e8;
  border: 1px solid #4caf50;
}

.boolean-value.false {
  color: #c62828;
  background-color: #ffebee;
  border: 1px solid #f44336;
}

.boolean-value i {
  margin-right: 6px;
  font-size: 14px;
}

.signature-section h3 {
  margin-top: 0;
  margin-bottom: 15px;
  font-size: 18px;
  color: #333;
}

/* Related fields styles */
.related-field-row {
  background-color: #f8f9fa;
  border-left: 3px solid #007bff;
}

.related-field-row:hover {
  background-color: #f0f0f0;
}

.related-field-name {
  font-weight: 500;
  color: #666;
  font-size: 13px;
  padding-left: 30px;
}

.related-field-name .indent {
  color: #007bff;
  font-weight: 600;
}

.related-field-value {
  color: #555;
  font-size: 13px;
  padding-left: 10px;
}

.flag-value {
  display: inline-flex;
  align-items: center;
  font-weight: 600;
  padding: 4px 10px;
  border-radius: 15px;
  font-size: 12px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  background-color: #fff3cd;
  color: #856404;
  border: 1px solid #ffeaa7;
}

.comment-value {
  background-color: #e3f2fd;
  padding: 8px 12px;
  border-radius: 6px;
  border-left: 3px solid #2196f3;
  font-style: italic;
  color: #1565c0;
  white-space: pre-wrap;
  max-width: 100%;
  word-break: break-word;
}

.related-image {
  max-width: 200px;
  max-height: 150px;
  border: 2px solid #e0e0e0;
  border-radius: 6px;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
  transition: transform 0.2s ease;
}

.related-image:hover {
  transform: scale(1.05);
}

/* Responsive styles */
@media (max-width: 767px) {
  .form-html-view-container {
    padding: 15px;
  }

  .form-content {
    padding: 20px;
  }

  .form-header {
    flex-direction: column;
    align-items: flex-start;
  }

  .logo-container {
    margin-right: 0;
    margin-bottom: 15px;
  }

  .form-title {
    font-size: 20px;
  }

  .form-section {
    margin-bottom: 20px;
  }

  .section-header {
    padding: 8px 12px;
  }

  .section-header h3 {
    font-size: 15px;
  }

  .form-table th,
  .form-table td {
    padding: 10px;
  }

  /* Stack field name and value on mobile */
  .form-table tr {
    display: flex;
    flex-direction: column;
    border-bottom: 1px solid #eee;
    padding: 8px 0;
  }

  .form-table tr:last-child {
    border-bottom: none;
  }

  .form-table td {
    border-bottom: none;
    padding: 5px 10px;
  }

  .field-name {
    font-weight: 600;
    color: #666;
    font-size: 13px;
  }

  .field-column,
  .value-column {
    width: auto;
  }

  /* Related fields responsive styles */
  .related-field-name {
    padding-left: 20px;
    font-size: 12px;
  }

  .related-field-value {
    padding-left: 5px;
    font-size: 12px;
  }

  .related-image {
    max-width: 150px;
    max-height: 100px;
  }

  .flag-value {
    font-size: 11px;
    padding: 3px 8px;
  }

  .comment-value {
    padding: 6px 10px;
    font-size: 12px;
  }
}

/* Manager Signatures section styles */
.manager-signatures-section {
  margin-top: 30px;
  padding: 20px;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  background: #f8fafc;
}

.manager-signatures-section h3 {
  margin: 0 0 20px 0;
  color: #1e40af;
  font-size: 1.3rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 8px;
}

.manager-signatures-section h3::before {
  content: "👔";
  font-size: 1.2rem;
}

.manager-signatures-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
}

.manager-signature-item {
  background: white;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  padding: 16px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.manager-signature-header {
  margin-bottom: 12px;
  border-bottom: 1px solid #e5e7eb;
  padding-bottom: 8px;
}

.manager-signature-header h4 {
  margin: 0 0 4px 0;
  color: #1f2937;
  font-size: 1.1rem;
  font-weight: 600;
}

.signature-timestamp {
  margin: 0;
  color: #6b7280;
  font-size: 0.875rem;
  font-style: italic;
}

.manager-signature-display {
  text-align: center;
}

.manager-signature-image {
  max-width: 100%;
  max-height: 120px;
  border: 1px solid #d1d5db;
  border-radius: 4px;
  background: white;
}

/* Print styles for manager signatures */
@media print {
  .manager-signatures-section {
    page-break-inside: avoid;
    margin-top: 20px;
  }

  .manager-signatures-grid {
    grid-template-columns: 1fr 1fr;
  }

  .manager-signature-item {
    break-inside: avoid;
  }
}

/* Mobile responsive styles for manager signatures */
@media (max-width: 767px) {
  .manager-signatures-grid {
    grid-template-columns: 1fr;
    gap: 15px;
  }

  .manager-signature-item {
    padding: 12px;
  }

  .manager-signature-header h4 {
    font-size: 1rem;
  }

  .signature-timestamp {
    font-size: 0.8rem;
  }

  .manager-signature-image {
    max-height: 100px;
  }
}

/* PDF Link Styles */
.pdf-link-container {
  display: flex;
  align-items: center;
  margin: 5px 0;
}

.pdf-link {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  background-color: #f8f9fa;
  border: 1px solid #dc3545;
  border-radius: 6px;
  color: #dc3545;
  text-decoration: none;
  font-weight: 500;
  font-size: 14px;
  transition: all 0.2s ease;
  cursor: pointer;
}

.pdf-link:hover {
  background-color: #dc3545;
  color: white;
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(220, 53, 69, 0.2);
}

.pdf-link:active {
  transform: translateY(0);
}

.pdf-icon {
  font-size: 16px;
  color: inherit;
}

.pdf-text {
  font-weight: 500;
}

.external-icon {
  font-size: 12px;
  opacity: 0.8;
}

.no-pdf-text {
  color: #6c757d;
  font-style: italic;
  font-size: 14px;
  padding: 8px 12px;
  background-color: #f8f9fa;
  border-radius: 4px;
  border: 1px dashed #ddd;
}

/* Mobile PDF link styles */
@media (max-width: 767px) {
  .pdf-link {
    padding: 10px 14px;
    font-size: 15px;
    gap: 10px;
    width: 100%;
    justify-content: center;
  }

  .pdf-icon {
    font-size: 18px;
  }

  .external-icon {
    font-size: 14px;
  }

  .no-pdf-text {
    font-size: 15px;
    padding: 10px 14px;
  }
}

/* ===== ENHANCED FOLLOW-UP FORM STYLES ===== */

/* Follow-up Data Container */
.followup-data {
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  padding: 16px;
  margin-top: 8px;
  max-width: 100%;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

/* Follow-up Header */
.followup-header {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 12px;
  padding-bottom: 10px;
  border-bottom: 2px solid #e2e8f0;
  font-weight: 600;
  color: #374151;
  font-size: 15px;
}

.followup-header i {
  color: #8b5cf6;
  font-size: 16px;
  background: rgba(139, 92, 246, 0.1);
  padding: 6px;
  border-radius: 50%;
}

/* Follow-up Content */
.followup-content {
  font-size: 14px;
  line-height: 1.6;
}

/* Follow-up Form Structure */
.followup-form-structure {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.followup-main-field {
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.followup-field-row {
  display: flex;
  flex-wrap: wrap;
  align-items: flex-start;
  gap: 12px;
  padding: 12px 16px;
  background: #fafbfc;
  border-bottom: 1px solid #f1f5f9;
}

.followup-field-label {
  font-weight: 600;
  color: #374151;
  min-width: 150px;
  flex-shrink: 0;
  font-size: 14px;
}

.followup-field-value {
  color: #1f2937;
  flex: 1;
  word-break: break-word;
  font-size: 14px;
  line-height: 1.5;
}

/* Follow-up Field Type Styling */
.field-type-signature .followup-signature-image {
  max-width: 200px;
  max-height: 80px;
  border: 1px solid #d1d5db;
  border-radius: 4px;
  background: white;
}

.field-type-image .followup-image {
  max-width: 150px;
  max-height: 100px;
  border: 1px solid #d1d5db;
  border-radius: 4px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

/* Follow-up Related Fields */
.followup-related-fields {
  background: #f8fafc;
  border-top: 1px solid #e5e7eb;
}

.followup-related-field {
  display: flex;
  flex-wrap: wrap;
  align-items: flex-start;
  gap: 10px;
  padding: 8px 16px;
  border-bottom: 1px solid #f1f5f9;
}

.followup-related-field:last-child {
  border-bottom: none;
}

.followup-related-label {
  font-weight: 500;
  color: #6b7280;
  min-width: 120px;
  flex-shrink: 0;
  font-size: 13px;
  display: flex;
  align-items: center;
  gap: 6px;
}

.followup-related-label::before {
  /* content: "↳"; */
  color: #9ca3af;
  font-weight: bold;
}

.followup-related-value {
  color: #374151;
  flex: 1;
  word-break: break-word;
  font-size: 13px;
}

/* Follow-up Flag and Comment Values */
.followup-flag-value {
  display: flex;
  align-items: center;
  gap: 6px;
  color: #f59e0b;
  font-weight: 500;
  background: rgba(245, 158, 11, 0.1);
  padding: 4px 8px;
  border-radius: 12px;
  border: 1px solid rgba(245, 158, 11, 0.2);
}

.followup-flag-value i {
  color: #f59e0b;
  font-size: 12px;
}

.followup-comment-value {
  display: flex;
  align-items: flex-start;
  gap: 6px;
  color: #3b82f6;
  background: rgba(59, 130, 246, 0.05);
  padding: 6px 10px;
  border-radius: 4px;
  border-left: 2px solid #3b82f6;
  font-style: italic;
  line-height: 1.4;
}

.followup-comment-value i {
  color: #3b82f6;
  font-size: 12px;
  margin-top: 2px;
  flex-shrink: 0;
}

.followup-related-image {
  max-width: 120px;
  max-height: 80px;
  border-radius: 4px;
  border: 1px solid #d1d5db;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

/* Nested Follow-up Forms */
.followup-nested {
  background: #f1f5f9;
  border: 1px solid #e2e8f0;
  border-radius: 6px;
  margin-top: 8px;
  overflow: hidden;
}

.followup-nested-header {
  background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
  color: white;
  padding: 8px 12px;
  font-weight: 600;
  font-size: 12px;
  display: flex;
  align-items: center;
  gap: 6px;
}

.followup-nested-header i {
  font-size: 12px;
}

.followup-nested-content {
  padding: 10px 12px;
  background: white;
}

.followup-nested-field {
  display: flex;
  flex-wrap: wrap;
  align-items: flex-start;
  gap: 8px;
  padding: 4px 0;
  border-bottom: 1px solid #f3f4f6;
}

.followup-nested-field:last-child {
  border-bottom: none;
}

.followup-nested-label {
  font-weight: 500;
  color: #4b5563;
  min-width: 100px;
  flex-shrink: 0;
  font-size: 12px;
}

.followup-nested-value {
  color: #374151;
  flex: 1;
  word-break: break-word;
  font-size: 12px;
}

/* Follow-up Simple Display */
.followup-simple {
  color: #374151;
  font-style: italic;
  padding: 12px;
  background: white;
  border-radius: 6px;
  border: 1px solid #e5e7eb;
  text-align: center;
}

/* Enhanced Related Field Styling */
.related-field-row {
  background: #fafbfc;
  border-left: 3px solid #e5e7eb;
}

.related-field-row:hover {
  background: #f3f4f6;
  border-left-color: #d1d5db;
}

.related-field-name .indent {
  color: #6b7280;
  font-size: 13px;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 6px;
}

.related-field-name .indent::before {
  /* content: "↳"; */
  color: #9ca3af;
  font-weight: bold;
}

/* Enhanced Flag Value */
.flag-value {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #f59e0b;
  font-weight: 600;
  background: rgba(245, 158, 11, 0.1);
  padding: 6px 12px;
  border-radius: 20px;
  border: 1px solid rgba(245, 158, 11, 0.2);
}

.flag-value i {
  color: #f59e0b;
  font-size: 14px;
}

/* Enhanced Comment Value */
.comment-value {
  display: flex;
  align-items: flex-start;
  gap: 8px;
  color: #3b82f6;
  background: rgba(59, 130, 246, 0.05);
  padding: 10px 12px;
  border-radius: 6px;
  border-left: 3px solid #3b82f6;
  font-style: italic;
  line-height: 1.5;
}

.comment-value i {
  color: #3b82f6;
  font-size: 14px;
  margin-top: 2px;
  flex-shrink: 0;
}

/* Enhanced Related Image */
.related-image {
  max-width: 200px;
  max-height: 120px;
  border-radius: 6px;
  border: 2px solid #e5e7eb;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.related-image:hover {
  transform: scale(1.05);
  border-color: #d1d5db;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* Mobile Responsive for Follow-up Data */
@media (max-width: 768px) {
  .followup-data {
    padding: 12px;
    margin-top: 6px;
  }

  .followup-header {
    font-size: 14px;
    margin-bottom: 10px;
    gap: 8px;
  }

  .followup-header i {
    font-size: 14px;
    padding: 4px;
  }

  .followup-content {
    font-size: 13px;
  }

  .followup-field-row {
    flex-direction: column;
    gap: 6px;
    padding: 8px 12px;
  }

  .followup-field-label {
    min-width: auto;
    font-size: 13px;
    margin-bottom: 2px;
  }

  .followup-field-value {
    font-size: 13px;
  }

  .followup-related-field {
    flex-direction: column;
    gap: 4px;
    padding: 6px 12px;
  }

  .followup-related-label {
    min-width: auto;
    font-size: 12px;
  }

  .followup-related-value {
    font-size: 12px;
  }

  .followup-flag-value,
  .followup-comment-value {
    font-size: 12px;
    padding: 4px 8px;
  }

  .followup-related-image {
    max-width: 100px;
    max-height: 60px;
  }

  .followup-nested-field {
    flex-direction: column;
    gap: 2px;
  }

  .followup-nested-label {
    min-width: auto;
    font-size: 11px;
  }

  .followup-nested-value {
    font-size: 11px;
  }

  .related-field-name .indent {
    font-size: 12px;
  }

  .flag-value,
  .comment-value {
    font-size: 12px;
    padding: 4px 8px;
  }

  .related-image {
    max-width: 150px;
    max-height: 100px;
  }
}

/* ===== FOLLOW-UP FORMS AS SEPARATE FORMS STYLES ===== */

/* Follow-up Forms Section */
.followup-forms-section {
  margin-top: 40px;
  padding-top: 30px;
  border-top: 3px solid #8b5cf6;
}

.followup-forms-container {
  display: flex;
  flex-direction: column;
  gap: 30px;
}

/* Individual Follow-up Form Wrapper */
.followup-form-wrapper {
  background: linear-gradient(135deg, #fafbfc 0%, #f8fafc 100%);
  border: 2px solid #e2e8f0;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  position: relative;
}

.followup-form-wrapper::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #8b5cf6 0%, #6366f1 50%, #3b82f6 100%);
}

/* Follow-up Form Header */
.followup-form-header {
  background: linear-gradient(135deg, #8b5cf6 0%, #6366f1 100%);
  color: white;
  padding: 15px 20px;
  position: relative;
  overflow: hidden;
}

.followup-form-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
}

.followup-form-title-section {
  position: relative;
  z-index: 1;
}

.followup-form-title {
  margin: 0 0 8px 0;
  font-size: 22px;
  font-weight: 700;
  display: flex;
  align-items: center;
  gap: 10px;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.followup-form-title i {
  font-size: 18px;
  color: rgba(255, 255, 255, 0.9);
  background: rgba(255, 255, 255, 0.2);
  padding: 8px;
  border-radius: 50%;
}

/* Follow-up Form Content */
.followup-form-content {
  padding: 0px;
  background: white;
}

/* Follow-up Form Sections */
.followup-section {
  margin-bottom: 25px;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.08);
  border: 1px solid #e8e8e8;
}

.followup-section-header {
  background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
  padding: 14px 18px;
  border-radius: 8px 8px 0 0;
  position: relative;
}

.followup-section-header::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
}

.followup-section-header h3 {
  margin: 0;
  font-size: 16px;
  color: white;
  font-weight: 600;
  position: relative;
  z-index: 1;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

/* Follow-up Form Table */
.followup-table {
  width: 100%;
  border-collapse: collapse;
  margin-bottom: 0;
  background-color: white;
  border: 1px solid #e0e0e0;
  border-top: none;
  border-radius: 0 0 8px 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.followup-table th,
.followup-table td {
  padding: 14px 18px;
  text-align: left;
  border-bottom: 1px solid #f0f0f0;
  vertical-align: top;
}

.followup-table tr:last-child td {
  border-bottom: none;
}

.followup-table tr:hover {
  background-color: #fafafa;
  transition: background-color 0.2s ease;
}

/* Follow-up Form Field Styling */
.followup-form-wrapper .field-name {
  font-weight: 600;
  color: #374151;
  font-size: 14px;
  background: rgba(99, 102, 241, 0.05);
  border-left: 3px solid #6366f1;
}

.followup-form-wrapper .field-value {
  color: #1f2937;
  font-size: 14px;
}

.followup-form-wrapper .related-field-row {
  background-color: #f8fafc;
  border-left: 3px solid #8b5cf6;
}

.followup-form-wrapper .related-field-row:hover {
  background-color: #f1f5f9;
}

.followup-form-wrapper .related-field-name {
  font-weight: 500;
  color: #6b7280;
  font-size: 13px;
  padding-left: 25px;
}

.followup-form-wrapper .related-field-name .indent {
  color: #8b5cf6;
  font-weight: 600;
}

.followup-form-wrapper .related-field-value {
  color: #374151;
  font-size: 13px;
}

/* Enhanced styling for follow-up form elements */
.followup-form-wrapper .flag-value {
  background: rgba(245, 158, 11, 0.15);
  border: 1px solid rgba(245, 158, 11, 0.3);
  color: #d97706;
}

.followup-form-wrapper .comment-value {
  background: rgba(59, 130, 246, 0.1);
  border-left: 3px solid #3b82f6;
  color: #1d4ed8;
}

/* Mobile Responsive for Follow-up Forms */
@media (max-width: 768px) {
  .followup-forms-section {
    margin-top: 30px;
    padding-top: 20px;
  }

  .followup-forms-container {
    gap: 15px;
  }

  .followup-form-wrapper {
    border-radius: 8px;
    margin: 0 -10px;
  }

  .followup-form-header {
    padding: 16px 20px;
  }

  .followup-form-title {
    font-size: 18px;
    gap: 8px;
  }

  .followup-form-title i {
    font-size: 20px;
    padding: 6px;
  }

  .followup-form-subtitle {
    font-size: 13px;
  }

  .followup-section {
    margin-bottom: 20px;
  }

  .followup-section-header {
    padding: 10px 14px;
  }

  .followup-section-header h3 {
    font-size: 14px;
  }

  .followup-table th,
  .followup-table td {
    padding: 10px 14px;
  }

  .followup-form-wrapper .field-name,
  .followup-form-wrapper .field-value {
    font-size: 13px;
  }

  .followup-form-wrapper .related-field-name,
  .followup-form-wrapper .related-field-value {
    font-size: 12px;
  }
}
