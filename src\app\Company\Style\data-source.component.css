.data-source-container{
  height: calc(100vh - 90px);
  display: flex;
  gap: 15px;
  overflow-y: auto;
  background-color: #f8fafc;
  font-family: '<PERSON><PERSON><PERSON>';
}
.creationDataSource{
  display: flex;
  gap: 15px;
}
.SpParameters{
  display: inline-grid;
  grid-template-columns: repeat(auto-fit, minmax(195px, 1fr));
  gap: 20px;
  padding: 2px;

}
.SpParameters-input{
  display: flex;
  flex-direction: column;
  gap: 5px;
}


/* Model For entering a Name a DataSource */
.modal-backdrop {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0,0,0,0.4);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-content {
  background: #fff;
  padding: 2rem;
  border-radius: 12px;
  width: 90vw;
  height:90vh;
  max-width: 90%;
  box-shadow: 0 10px 25px rgba(0,0,0,0.2);
  /* text-align: center; */
}

.modal-input {
  width: 15vw;
  /* padding: 10px; */
  /* margin: 1rem 0; */
  border: 1px solid #ccc;
  border-radius: 8px;
  font-size: 1rem;
}

.modal-actions {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

.modal-actions button {
  padding: 8px 16px;
  border: none;
  border-radius: 6px;
  background-color: #007bff;
  color: white;
  cursor: pointer;
}

.modal-actions .cancel {
  background-color: #ccc;
}

/* Preview data for user Data will be Comes From SP */
.table-container {
  max-height: 360px; /* set as needed */
  max-width: 100%;   /* or fixed width like 1000px */
  overflow-x: auto;
  overflow-y: auto;
  border: 1px solid #ccc;
  border-radius: 8px;
  margin-bottom: 10px;
}

.data-table {
  width: max-content; /* ensures wide tables don't shrink */
  border-collapse: collapse;
}

.data-table th, .data-table td {
  padding: 8px 12px;
  border: 1px solid #ddd;
  text-align: left;
  white-space: nowrap; /* prevents column wrapping */
}



