.data-source-container{
  height: calc(100vh - 90px);
  display: flex;
  gap: 15px;
  overflow-y: auto;
  background-color: #f8fafc;
  font-family: '<PERSON><PERSON><PERSON>';
}
.creationDataSource{
  display: flex;
  gap: 15px;
}
.SpParameters{
  display: inline-grid;
  grid-template-columns: repeat(auto-fit, minmax(195px, 1fr));
  gap: 20px;
  padding: 2px;

}
.SpParameters-input{
  display: flex;
  flex-direction: column;
  gap: 5px;
}


/* Enhanced Modal For entering a Name a DataSource */
.modal-backdrop {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, rgba(0, 0, 0, 0.4) 0%, rgba(0, 0, 0, 0.6) 100%);
  backdrop-filter: blur(4px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  animation: modalFadeIn 0.3s ease-out;
}

@keyframes modalFadeIn {
  from {
    opacity: 0;
    backdrop-filter: blur(0px);
  }
  to {
    opacity: 1;
    backdrop-filter: blur(4px);
  }
}

.modal-content {
  background: #fff;
  border-radius: 16px;
  width: 90vw;
  height: 90vh;
  max-width: 1200px;
  max-height: 90vh;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15), 0 8px 16px rgba(0, 0, 0, 0.1);
  animation: modalSlideIn 0.3s ease-out;
  border: 1px solid rgba(255, 255, 255, 0.2);
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

@keyframes modalSlideIn {
  from {
    transform: translateY(-20px) scale(0.95);
    opacity: 0;
  }
  to {
    transform: translateY(0) scale(1);
    opacity: 1;
  }
}

/* Modal Header */
.modal-content h3 {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  margin: 0;
  padding: 20px 24px;
  font-size: 1.2rem;
  font-weight: 600;
  border-radius: 16px 16px 0 0;
  flex-shrink: 0;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

/* Modal Body - Scrollable Content */
.modal-body {
  flex: 1;
  padding: 21px;
  overflow-y: auto;
  overflow-x: hidden;
  scrollbar-width: thin;
  scrollbar-color: #cbd5e0 #f7fafc;
}

/* Custom scrollbar for webkit browsers */
.modal-body::-webkit-scrollbar {
  width: 8px;
}

.modal-body::-webkit-scrollbar-track {
  background: #f7fafc;
  border-radius: 4px;
}

.modal-body::-webkit-scrollbar-thumb {
  background: #cbd5e0;
  border-radius: 4px;
}

.modal-body::-webkit-scrollbar-thumb:hover {
  background: #a0aec0;
}

/* Enhanced Form Inputs */
.modal-input {
  width: 100%;
  padding: 0px 3px;
  /* margin-bottom: 1rem; */
  border: 1px solid #d1d5db;
  border-radius: 8px;
  font-size: 14px;
  transition: all 0.2s ease;
  background: white;
}

.modal-input:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.modal-input:hover {
  border-color: #9ca3af;
}

/* Creation Data Source Section */
.creationDataSource {
  margin-bottom: 5px;
}

.creationDataSource button {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  color: white;
  border: none;
  padding: 9px 20px;
  border-radius: 8px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  margin-top: 0px;
  width: 20%;
}

.creationDataSource button:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
}

/* SP Parameters Section */
.SpParameters {
  margin-bottom: 16px;
}

.SpParameters-input {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.SpParameters-input label {
  font-weight: 500;
  color: #374151;
  font-size: 14px;
}

/* Enhanced Modal Actions - Always Visible at Bottom */
.modal-actions {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  gap: 12px;
  padding: 20px 24px;
  background: #f8fafc;
  border-top: 1px solid #e2e8f0;
  flex-shrink: 0;
  border-radius: 0 0 16px 16px;
}

.modal-actions button {
  padding: 12px 24px;
  border: none;
  border-radius: 8px;
  font-weight: 500;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 8px;
  min-width: 100px;
  justify-content: center;
}

/* Primary Save Button */
.modal-actions button:not(.cancel) {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
}

.modal-actions button:not(.cancel):hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
}

.modal-actions button:not(.cancel):active {
  transform: translateY(0);
}

/* Cancel Button */
.modal-actions .cancel {
  background: white;
  color: #6b7280;
  border: 1px solid #d1d5db;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.modal-actions .cancel:hover {
  background: #f9fafb;
  border-color: #9ca3af;
  color: #374151;
  transform: translateY(-1px);
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
}

.modal-actions .cancel:active {
  transform: translateY(0);
}

/* Enhanced Modal Responsive Design */
@media (max-width: 768px) {
  .modal-content {
    width: 95vw;
    height: 95vh;
    margin: 10px;
    border-radius: 12px;
  }

  .modal-content h3 {
    padding: 16px 20px;
    font-size: 1.1rem;
  }

  .modal-body {
    padding: 20px;
  }

  .modal-actions {
    padding: 16px 20px;
    gap: 10px;
  }

  .modal-actions button {
    padding: 10px 20px;
    font-size: 13px;
    min-width: 80px;
  }
}

@media (max-width: 480px) {
  .modal-content {
    width: 98vw;
    height: 98vh;
    margin: 5px;
    border-radius: 8px;
  }

  .modal-content h3 {
    padding: 14px 16px;
    font-size: 1rem;
  }

  .modal-body {
    padding: 16px;
  }

  .modal-actions {
    padding: 14px 16px;
    flex-direction: column;
    gap: 8px;
  }

  .modal-actions button {
    width: 100%;
    padding: 12px;
    font-size: 14px;
  }
}

/* Preview data for user Data will be Comes From SP */
.table-container {
  max-height: 360px; /* set as needed */
  max-width: 100%;   /* or fixed width like 1000px */
  overflow-x: auto;
  overflow-y: auto;
  border: 1px solid #ccc;
  border-radius: 8px;
  margin-bottom: 10px;
}

.data-table {
  width: max-content; /* ensures wide tables don't shrink */
  border-collapse: collapse;
}

.data-table th, .data-table td {
  padding: 8px 12px;
  border: 1px solid #ddd;
  text-align: left;
  white-space: nowrap; /* prevents column wrapping */
}



