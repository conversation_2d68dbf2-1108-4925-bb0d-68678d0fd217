export interface FormSchema {
 id?: string;
  auditHistory: {
    userName?: string;
    formName?: string;
    location?: string;
    createdBy?: string;
    updatedBy?: string;
    createdDate?: Date;
    updatedDate?: Date;
    status?: string;
    userID?: string;
  };
  components: FormComponent[];
}

export interface FormMeta {
  formTitle: string;
  createdBy?: string;
}

export type FormComponent =
  | HeaderComponent
  | ParagraphComponent
  | TextComponent
  | TextareaComponent
  | SelectComponent
  | FileComponent
  | DateComponent
  | SignatureComponent
  | MapComponent
  | QRScannerComponent
  | LinkComponent
  | CalculatedComponent
  | ConditionComponent
  | RepeaterComponent
  | TableStaticComponent
  | TableDynamicComponent
  | TimeSpanComponent
  | LockComponent
  | PageBreakComponent;


export interface BaseComponent {
  type: string;
  attributes?: any;
}

export interface Style {
  [key: string]: string;
}

export interface ActionModel {
  comment?: boolean;
  camera?: boolean;
  flag?: boolean;
}




export interface HeaderComponent extends BaseComponent {
  type: 'header';
  attributes: {
    text?: string;
    level?: number;
    style?: Style;
  };
}

export interface ParagraphComponent extends BaseComponent {
  type: 'paragraph';
  attributes: {
    text: string;
    style?: Style;
  };
}

export interface TextComponent extends BaseComponent {
  type: 'text';
  attributes: {
    label: string;
    field_Id: string;
    is_required: boolean;
    placeholder_text?: string;
    show_label?: boolean;
    default_value?: string;
    validations?: any;
    style?: Style;
    actions?: ActionModel;
  };
}

export interface TextareaComponent extends BaseComponent {
  type: 'textarea';
  attributes: {
    label: string;
    field_Id: string;
    is_required: boolean;
    placeholder_text?: string;
    show_label?: boolean;
    default_value?: string;
    validations?: any;
    style?: Style;
    actions?: ActionModel;
  };
}


export interface SelectComponent extends BaseComponent {
  type: 'Select';
  multiselect: boolean;
  attributes: {
    label: string;
    field_Id: string;
    is_required: boolean;
    placeholder_text?: string;
    show_label?: boolean;
    dataListId: string;
    actions?: ActionModel;
  };
}

export interface FileComponent extends BaseComponent {
  type: 'file';
  attributes: {
    label: string;
    field_Id: string;
    is_required: boolean;
    show_label?: boolean;
    placeholder_text?: string;
    validations?: any;
    actions?: ActionModel;
  };
}

export interface DateComponent extends BaseComponent {
  type: 'date';
  attributes: {
    label: string;
    field_Id: string;
    is_required: boolean;
    show_label?: boolean;
    validations?: any;
    actions?: ActionModel;
  };
}

export interface SignatureComponent extends BaseComponent {
  type: 'signature';
  attributes: {
    label: string;
    field_Id: string;
    is_required: boolean;
    show_label?: boolean;
    pen_color?: string;
    actions?: ActionModel;
  };
}

export interface MapComponent extends BaseComponent {
  type: 'map';
  attributes: {
    label: string;
    field_Id: string;
    is_required: boolean;
    show_label?: boolean;
    default_lat?: number;
    default_lng?: number;
    actions?: ActionModel;
  };
}

export interface QRScannerComponent extends BaseComponent {
  type: 'qrscanner';
  attributes: {
    label: string;
    field_Id: string;
    is_required: boolean;
    show_label?: boolean;
    actions?: ActionModel;
  };
}

export interface LinkComponent extends BaseComponent {
  type: 'link';
  attributes: {
    label: string;
    url: string;
    link_text: string;
    show_label?: boolean;
    is_required?: boolean;
    actions?: ActionModel;
  };
}


export interface CalculatedComponent extends BaseComponent {
  type: 'calculated';
  attributes: {
    label: string;
    field_Id: string;
    formula: string;
    dependencies: string[];
    style?: Style;
  };
}


export interface ConditionComponent extends BaseComponent {
  type: 'condition';
  attributes: {
    label?: string;
    condition: {
      field: string;
      operator: '==' | '!=' | '>' | '<' | '>=' | '<=';
      value: any;
    };
    children: FormComponent[];
  };
}


export interface RepeaterComponent extends BaseComponent {
  type: 'repeater';
  attributes: {
    label: string;
    field_Id: string;
    template: {
      type: 'group';
      elements: FormComponent[];
    };
  };
}


export interface TableStaticComponent extends BaseComponent {
  type: 'table-static';
  attributes: {
    label: string;
    columns: string[];
    rows: string[][];
  };
}


export interface TableDynamicComponent extends BaseComponent {
  type: 'table-dynamic';
  attributes: {
    label: string;
    field_Id: string;
    columns: string[];
    addRowText?: string;
  };
}

export interface TimeSpanComponent extends BaseComponent {
  type: 'timespan';
  attributes: {
    label: string;
    field_Id: string;
    start_field: string;
    end_field: string;
    style?: Style;
  };
}


export interface LockComponent extends BaseComponent {
  type: 'lock';
  attributes: {
    label: string;
    field_Id: string;
    value: string;
    readOnly: boolean;
  };
}

export interface PageBreakComponent extends BaseComponent {
  type: 'pagebreak';
  attributes?: {}; // No attributes
}

