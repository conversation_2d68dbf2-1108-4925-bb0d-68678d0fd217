import { Component } from '@angular/core';
import { CoreDataService } from '../../core-data.service';
import { DropdownData, FormListData, ListValue } from '../../home/<USER>/model';
import { FormBuilder } from '@angular/forms';
import { switchMap } from 'rxjs/operators';
import { ShareService } from '../../SharedData/share-services.service';

@Component({
  selector: 'app-form-list',
  templateUrl: './../Template/form-list.component.html',
  styleUrl: './../Styles/form-list.component.css'
})
export class FormListComponent {
  constructor(private coreData:CoreDataService, public shareService:ShareService){}
  lists: DropdownData[]=[];
  selectedList: DropdownData | null = null;
  editingItem: FormListData | null = null;
  showPopup = false;
  isListGroup: boolean = false;
  newListName = '';

    // Form List
    formData: DropdownData = {
      auditHistory: {
        listName: '',
      },
      list: []
    };

  ngOnInit(): void {
    this.formData.auditHistory.location = this.shareService.selectedLocation;
    this.coreData.getallFormList().subscribe({
      next:(data)=>{
        this.lists=data;
      },
      error:(err)=>{
        console.log(err);
      }
    })
  }
  // Add a new list
  addList() {
    this.formData = {
      auditHistory: {
        listName: '',
        location: this.shareService.selectedLocation,
      },
      list: [
      {
          title: '', items:
        [
          { value: '' },
          { value: '' }
        ]
      }
      ]
    };
    const name = this.newListName.trim();
    if (name) {
      this.formData.auditHistory.listName = name;
      this.selectedList = this.formData;
      this.newListName = '';
      this.showPopup = false;
    }
  }

  selectList(list: DropdownData) {
    this.selectedList = list;
    this.coreData.getFormListByID(this.selectedList?.id||'').subscribe({
      next:(data)=>{
        this.formData=data;
      },
      error:(err)=>{
        console.log(err);
      }
    });
  }
  addItem() {
    const newItem: FormListData = {
      title: 'New Item',
      items: [
        { value: '' }
      ]
    };

    this.formData.list.push(newItem);
  }
  editItem(item: FormListData) {
    this.editingItem = item;
  }
  deleteItem(index: number) {
    this.formData.list.splice(index, 1);
  }
  addSubItem(index: number) {
    const newSubItem: ListValue = { value: '' };
    this.formData.list[index].items.push(newSubItem);
  }

  removeSubItem(itemIndex: number, valueIndex: number) {
    this.formData.list[itemIndex].items.splice(valueIndex, 1);
  }
  saveList() {
    if (!this.formData || !this.formData.auditHistory.listName || !this.formData.auditHistory.listName.trim()) {
      alert('Please enter a list name');
      return;
    }

    // Make sure we have at least one item with values
    if (this.formData.list.length === 0) {
      alert('Please add at least one item to the list');
      return;
    }

    // Check if all items have at least one value
    const emptyItems = this.formData.list.filter(item => item.items.length === 0);
    if (emptyItems.length > 0) {
      alert('Please add at least one value to each item');
      return;
    }

    // Save to the API
    this.coreData.saveFormlist(this.formData).pipe(
      switchMap(() => this.coreData.getListofLocation(this.shareService.selectedLocation))
    ).subscribe({
      next: (data) => {
        console.log('List saved and refreshed successfully.');
        this.shareService.lists = data;
        this.formData = {
          auditHistory: {
            listName: '',
            location: this.shareService.selectedLocation,
          },
          list: []
        };
        this.selectedList = null;
      },
      error: (err) => {
        console.error('Error saving or fetching lists:', err);
      }
    });

  }

}
