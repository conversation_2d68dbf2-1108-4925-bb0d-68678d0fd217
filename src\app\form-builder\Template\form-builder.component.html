<div class="form-builder-container">

  <!-- Sidebar -->
  <div class="sidebar" [class.collapsed]="isSidebarCollapsed">
    <!-- Forms List View -->
    <div class="sidebar-header">
      <h3>Form Builder</h3>
      <button class="add-form-btn" (click)="startNewForm()">
        <i class="fas fa-plus"></i>
      </button>
    </div>
    <div class="sidebar-nav">
      <ul class="nav-list">
        <li *ngFor="let form of this.formsListforBuilder" class="nav-item">
          <div class="nav-link"
               [class.active]="selectedFormId === form.id"
               (click)="selectForm(form)">
            <span class="nav-text">{{ form.auditHistory.formName || 'Unnamed Form' }}</span>
          </div>
        </li>
      </ul>
      <div *ngIf="this.formsListforBuilder.length === 0" class="no-forms">
        No forms available. Click + to create one.
      </div>
    </div>
  </div>
  <!-- Main Content -->
  <div class="main-content" [class.expanded]="isSidebarCollapsed" [class.expanded-right]="isRightSidebarCollapsed">
    <button class="sidebar-toggle" (click)="toggleSidebar()">
      <i class="fas" [ngClass]="{'fa-chevron-right': isSidebarCollapsed, 'fa-chevron-left': !isSidebarCollapsed}"></i>
    </button>
    <button class="Right-sidebar-toggle" (click)="RighttoggleSidebar()">
      <i class="fas" [ngClass]="{'fa-chevron-left': isRightSidebarCollapsed, 'fa-chevron-right': !isRightSidebarCollapsed}"></i>
    </button>

     <!-- Sub Navigation Bar -->
    <div class="sub-navbar" *ngIf="selectedFormId">
      <div class="sub-nav-left">
        <div class="form-title-display">
          <i class="fas fa-file-alt"></i>
          <span>Configration</span>
        </div>
      </div>

      <div class="sub-nav-center">
        <div class="nav-button-group">
          <button class="nav-btn" (click)="addSection()" title="Add Section">
            <i class="fas fa-plus"></i>
            <span class="btn-text">Section</span>
          </button>

          <button class="nav-btn" (click)="previewForm()" title="Preview Form">
            <i class="fas fa-eye"></i>
            <span class="btn-text">Preview</span>
          </button>

          <button class="nav-btn" (click)="saveForm()" title="Save Form">
            <i class="fas fa-save"></i>
            <span class="btn-text">Save</span>
          </button>

          <button class="nav-btn" title="Duplicate Form">
            <i class="fas fa-copy"></i>
            <span class="btn-text">Duplicate</span>
          </button>
        </div>
      </div>

      <div class="sub-nav-right">
          <div class="nav-button-group">
          <button class="nav-btn" (click)="exportForm()" title="Export Form">
            <i class="fas fa-download"></i>
            <span class="btn-text">Export</span>
          </button>

          <button class="nav-btn" title="Share Form">
            <i class="fas fa-share-alt"></i>
            <span class="btn-text">Share</span>
          </button>

          <button class="nav-btn danger" title="Delete Form">
            <i class="fas fa-trash"></i>
            <span class="btn-text">Delete</span>
          </button>
        </div>

      </div>
    </div>

    <!-- Form Builder Content -->
    <ng-container>
      <!-- Initial State - Only New Form Button -->
      <div class="initial-state" *ngIf="!selectedFormId && (!formData || formData.component.length === 0)">
        <div class="no-selection">
          <p>Select a form from the sidebar to edit or create a new one</p>
          <button class="new-form-btn" (click)="startNewForm()">
            <i class="fas fa-plus-circle"></i> New Form
          </button>
        </div>
      </div>

      <div class="box-content">

      <!-- Main Form Building Area - Only shown when form is selected -->
      <div class="form-building-area" *ngIf="selectedFormId && formData && formData.component.length > 0">
        <!-- Form Header with Form Name -->
        <div class="form-header">
          <h2 *ngIf="formData.auditHistory.formName">{{ formData.auditHistory.formName || 'Untitled Form' }}</h2>
          <div class="form-metadata">
            <div class="required-fields-note">* Indicates Required Fields</div>
          </div>
        </div>

        <!-- Form Sections -->
        <div class="form-preview">
          <div *ngFor="let section of formData.component; let sectionIndex = index" class="form-section">
            <!-- Section Header -->
            <div class="section-header">
              <div class="section-title">
                <i class="fas fa-bars drag-handle"></i>
                <input type="text" [(ngModel)]="section.title" (ngModelChange)="updateSectionTitle(section, $event)" placeholder="New Section">
              </div>
              <div class="section-actions">
                <div class="section-toggles">
                  <div class="toggle-option">
                    <input type="checkbox" id="collapsible-{{sectionIndex}}"
                          [(ngModel)]="section.canCollapsed"
                          (change)="section.isCollapsed = section.canCollapsed ? section.isCollapsed : false">
                    <label for="collapsible-{{sectionIndex}}">Collapsible</label>
                  </div>

                  <div class="toggle-option">
                    <input type="checkbox" id="repeatable-{{sectionIndex}}"
                    [(ngModel)]="section.repeatable"
                    (change)="section.repeatable = section.repeatable ? section.repeatable : false">
                    <label for="repeatable-{{sectionIndex}}">Repeatable</label>
                  </div>
                  <div class="toggle-option">
                    <input type="checkbox" id="conditional-{{sectionIndex}}"
                          (change)="handleConditionToggle(section)">
                    <label for="conditional-{{sectionIndex}}">Conditional Section</label>
                  </div>
                </div>
                <button class="btn-icon collapse-btn" *ngIf="section.canCollapsed" (click)="toggleSectionCollapse(section)">
                  <i class="fas" [ngClass]="{'fa-chevron-down': !section.isCollapsed, 'fa-chevron-up': section.isCollapsed}"></i>
                </button>
                <button class="btn-icon delete-section" (click)="formData.component.splice(sectionIndex, 1)">
                  <i class="fas fa-trash"></i>
                </button>
              </div>
            </div>

<!-- Condition Popup -->
<div *ngIf="showAdvancedConditionModal" class="modal">
  <div class="modal-content" style="width: 60vw; height: 84vh; overflow-y: auto;">
    <div class="modal-header">
      <h3><i class="fas fa-code-branch"></i> Set Section Condition</h3>
      <button class="close-btn" (click)="cancelCondition()">
        <i class="fas fa-times"></i>
      </button>
    </div>

    <div class="modal-body">
      <div class="validation-group">

            <div class="logic-toggle" *ngIf="section.conditionalAtSection?.conditions">
              <label>
                <input type="radio" [(ngModel)]="section.conditionalAtSection!.logic" value="AND" /> And
              </label>
              <label>
                <input type="radio" [(ngModel)]="section.conditionalAtSection!.logic" value="OR" /> Or
              </label>
            </div>

              <!-- Preview -->
               <div class="condition-preview" *ngIf="section.conditionalAtSection?.conditions!.length > 0">
                  <div class="preview-header">
                    <i class="fas fa-eye"></i> Preview
                  </div>
                  <div class="preview-text">
                    Show this field when
                    <span *ngFor="let cond of section.conditionalAtSection?.conditions; let i = index">
                      <strong>{{ cond.field }}</strong>
                      <span class="operator">{{ cond.operator === '==' ? 'equals' : 'does not equal' }}</span>
                      <strong>"{{ cond.value }}"</strong>
                      {{i < section.conditionalAtSection?.conditions!.length - 1 ? section.conditionalAtSection?.logic === 'AND' ? 'and' : 'or' : ''}}
                    </span>
                  </div>
                </div>
               <!-- Conditions List -->
              <div class="condition-row" *ngFor="let cond of section.conditionalAtSection?.conditions; let i = index" >

                <div style="display: flex; gap: 1vw;">
                <select class="Condition-input" [(ngModel)]="cond.field">
                    <option value="">Select Field</option>
                    <option *ngFor="let field of availableFields" [value]="field.fieldName">{{ field.label }}</option>
                  </select>

                  <select class="Condition-input" [(ngModel)]="cond.operator">
                    <option value="==">is equal to</option>
                    <option value="!=">is not equal to</option>
                  </select>

                  <input class="Condition-input" [(ngModel)]="cond.value" placeholder="Enter value" />
                </div>

                  <!-- Remove condition -->
                  <button style="color: red;" (click)="removeConditionForSection(i, section)" title="Remove Condition" ><i class="fas fa-trash"></i></button>

                </div>

                <!-- Add condition -->
                <button class="btn-advanced-validation" (click)="addConditionForSection(section)">+ Add Condition</button>
          </div>
    </div>
    <div class="modal-validation-footer">
          <button class="validation-Submit-btn" (click)='this.showAdvancedConditionModal = false'>Done</button>
      </div>
  </div>
</div>


            <!-- Section Content -->
            <div class="section-content" *ngIf="!section.isCollapsed">
              <div class="empty-section-message" *ngIf="section.elements.length === 0">
                <p>This section is empty. Add form elements.</p>
              </div>

              <!-- Form Elements would go here -->
              <div class="form-elements-list">
                <div *ngFor="let element of section.elements; let elementIndex = index"
                     class="form-element-row"
                     [class.selected-element]="selectedElement === element"
                     (click)="selectElementForValidation(element, section)">
                  <!-- Required Star Icon -->
                  <div class="element-required" (click)="toggleRequired(element); $event.stopPropagation()">
                    <i class="fas fa-star" [ngClass]="{'required': element.attributes.is_required, 'not-required': !element.attributes.is_required}"></i>
                  </div>

                  <!-- Element Type Icon -->
                  <div class="element-type-icon">
                    <i *ngIf="element.type === 'text'" class="fas fa-font"></i>
                    <!-- <i *ngIf="element.type === 'email'" class="fas fa-envelope"></i>
                    <i *ngIf="element.type === 'number'" class="fas fa-hashtag"></i> -->
                    <i *ngIf="element.type === 'link'" class="fas fa-link"></i>
                    <i *ngIf="element.type === 'Select'" class="fas fa-check-square"></i>
                  </div>

                  <!-- Element Label and Placeholder/Link Text -->
                  <div class="element-fields" (click)="$event.stopPropagation()">
                    <div class="element-label">
                      <input type="text" [(ngModel)]="element.attributes.label"

                            placeholder="Label">
                    </div>
                    <!-- For regular elements with placeholder -->
                    <div class="element-placeholder" *ngIf="element.type !== 'map' && element.type !== 'date' && element.type !== 'link' && element.type !== 'signature'  ">
                      <input type="text" [(ngModel)]="element.attributes.placeholder_text" placeholder="Placeholder">
                    </div>
                    <!-- For link elements -->
                    <div class="element-link-text" *ngIf="element.type === 'link'">
                      <input type="text" [(ngModel)]="element.attributes.link_text">
                    </div>
                    <!-- For dropdown elements -->
                    <!-- <div class="element-dropdown-info" *ngIf="element.type === 'select'">
                      <span class="dropdown-source">List ID: {{ element.attributes.dataListId }}</span>
                    </div> -->
                  </div>

                  <!-- Element Actions -->
                  <div class="element-actions">
                    <button class="btn-icon delete-btn" (click)="deleteElement(section, elementIndex); $event.stopPropagation()">
                      <i class="fas fa-trash"></i>
                    </button>
                  </div>
                  <div class="options" >
                    <i class="fas fa-ellipsis-v"></i>
                  </div>

                </div>
              </div>

              <!-- Add Item Button -->
              <div class="add-item-container">
                <button class="btn-add-small" (click)="showElementSelectionSidebar(section)">
                  <i class="fas fa-plus"></i> Add Item
                </button>
              </div>
            </div>
          </div>

          <!-- Add Section Button -->
          <div class="add-section-container">
            <button class="btn-add-small" (click)="addSection()">
              <i class="fas fa-plus"></i> Add Section
            </button>
          </div>
        </div>

        <!-- Form Actions -->
        <div class="form-actions">
          <button class="btn-save" (click)="saveForm()">
            <i class="fas fa-save"></i> Save Form
          </button>
        </div>
      </div>

      <!-- Validation Panel -->
      <div class="right-sidebar" *ngIf="selectedElement">
          <div class="validation-box">
            <!-- Properties Header -->
          <div class="validation-header">
              <h3>Element Properties</h3>
          </div>
          <div class="validation-content">



              <!-- Required Field Toggle -->
              <div class="validation-group toggle-group">
              <label class="validation-label">Required Field</label>
              <label class="switch">
                <input type="checkbox"
                      [(ngModel)]="selectedElement.attributes.is_required">
                <span class="slider round"></span>
              </label>
            </div>

            <!-- Show Label Toggle -->
              <div class="validation-group toggle-group" >
              <label class="validation-label">Show Label</label>
              <label class="switch">
                <input type="checkbox"
                      [(ngModel)]="selectedElement.attributes.show_label">
                <span class="slider round"></span>
              </label>
            </div>

            <!-- Show Multiselect Toggle -->
          <div class="validation-group toggle-group" *ngIf="selectedElement.type == 'Select'" style="display: flex; flex-direction: column;">
            <div style="display: flex; gap: 10vw;">
            <label class="validation-label">Multiselect</label>
              <label class="switch">
                <input type="checkbox"
                      [(ngModel)]="selectedElement.multiselect"
                      (change)="onMultiselectToggle($event)">
                <span class="slider round"></span>
              </label></div>
              <!-- Selected List Display and Change Option -->
              <div>
                <div class="list-selector-container">
                  <div class="current-list-display" *ngIf="selectedElement.attributes.dataListId">
                    <span class="list-name">{{ getListNameById(selectedElement.attributes.dataListId) }}</span>
                    <button type="button" class="change-list-btn" (click)="toggleList()">
                      <i class="fas fa-edit"></i> Change
                    </button>
                  </div>
                  <div class="no-list-selected" *ngIf="!selectedElement.attributes.dataListId">
                    <span class="no-list-text">System Genrated List</span>
                    <button type="button" class="select-list-btn" (click)="toggleList()">
                      <i class="fas fa-plus"></i> Select List
                    </button>
                  </div>

                  <!-- List Dropdown -->
                  <div class="list-dropdown" *ngIf="showListSelector">
                    <div class="dropdown-search">
                      <input type="text"
                            [(ngModel)]="listSearchTerm"
                            (input)="filterLists()"
                            placeholder="Search lists..."
                            class="dropdown-search-input">
                    </div>
                    <div class="dropdown-list-items">
                      <div *ngIf="this.formLists.length === 0" class="no-lists-message">
                        No lists available. Please create lists first.
                      </div>
                      <div *ngIf="this.formLists.length > 0 && filteredFormLists.length === 0" class="no-lists-message">
                        No matching lists found.
                      </div>
                      <div *ngFor="let list of filteredFormLists"
                          class="dropdown-list-item"
                          [class.selected]="selectedElement.attributes.dataListId === list.id"
                          (click)="selectListForElement(list, selectedElement)">
                        {{ list.auditHistory.listName }}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>


              <!-- update Label/placeholder -->
              <div class = "validation-group" style="gap: 1vw; display: flex; flex-direction: column;">
                <div>
                <label class="validation-label">Enter Label:</label>
                <input type="text"
                    class="validation-input"
                      [value]="selectedElement.attributes.label"
                      (input)="updatelabel($event, selectedElement)"
                      placeholder = 'Enter Label '></div>
                <!-- Placeholder -->
                  <div *ngIf="selectedElement.type !== 'map' && selectedElement.type !== 'date' && selectedElement.type !== 'link' && selectedElement.type !== 'signature'">
                  <label class="validation-label">Placeholder:</label>
                  <input type="text"
                          class="validation-input"
                          [value]="selectedElement.attributes.placeholder_text"
                          (input)="UpatePlaceholder($event, selectedElement)"
                          placeholder="Enter Placeholder">
                  </div>
              </div>


                <!-- Update Link Test For show -->
                <div class=" validation-group" *ngIf="selectedElement.type === 'link'">
                  <label class="validation-label">Showing Text</label>
                  <input type="text"
                      class="validation-input"
                      [(ngModel)]="selectedElement.attributes.link_text">
                </div>

              <!-- Text/Textarea Validations -->
              <div *ngIf="isValidationApplicable('minlength')" class="validation-group">
                <label class="validation-label">Minimum Length:</label>
                <input type="number"
                        class="validation-input"
                        [value]="getValidationValue('minlength')"
                        (input)="onValidationInputChange($event, 'minlength')"
                        placeholder="e.g., 3">
              </div>

              <div *ngIf="isValidationApplicable('maxlength')" class="validation-group">
                <label class="validation-label">Maximum Length:</label>
                <input type="number"
                        class="validation-input"
                        [value]="getValidationValue('maxlength')"
                        (input)="onValidationInputChange($event, 'maxlength')"
                        placeholder="e.g., 50">
              </div>

              <!-- Number Validations -->
              <div *ngIf="isValidationApplicable('min')" class="validation-group">
                <label class="validation-label">Minimum Value:</label>
                <input type="number"
                        class="validation-input"
                        [value]="getValidationValue('min')"
                        (input)="onValidationInputChange($event, 'min')"
                        placeholder="e.g., 0">
              </div>

              <div *ngIf="isValidationApplicable('max')" class="validation-group">
                <label class="validation-label">Maximum Value:</label>
                <input type="number"
                        class="validation-input"
                        [value]="getValidationValue('max')"
                        (input)="onValidationInputChange($event, 'max')"
                        placeholder="e.g., 100">
              </div>

              <!-- Date Validations -->
              <div *ngIf="isValidationApplicable('minDate')" class="validation-group">
                <label class="validation-label">Minimum Date:</label>
                <input type="date"
                        class="validation-input"
                        [value]="getValidationValue('minDate')"
                        (input)="onValidationInputChange($event, 'minDate')">
              </div>

              <div *ngIf="isValidationApplicable('maxDate')" class="validation-group">
                <label class="validation-label">Maximum Date:</label>
                <input type="date"
                        class="validation-input"
                        [value]="getValidationValue('maxDate')"
                        (input)="onValidationInputChange($event, 'maxDate')">
              </div>

              <div *ngIf="isValidationApplicable('noFuture')" class="validation-group">
                <label class="validation-label">
                  <input type="checkbox"
                          [checked]="getValidationValue('noFuture') === 'true' || getValidationValue('noFuture') === true"
                          (change)="onValidationCheckboxChange($event, 'noFuture')">
                  No Future Dates
                </label>
              </div>

              <div *ngIf="isValidationApplicable('noPast')" class="validation-group">
                <label class="validation-label">
                  <input type="checkbox"
                          [checked]="getValidationValue('noPast') === 'true' || getValidationValue('noPast') === true"
                          (change)="onValidationCheckboxChange($event, 'noPast')">
                  No Past Dates
                </label>
              </div>

              <!-- File Validations -->
              <div *ngIf="isValidationApplicable('maxSize')" class="validation-group">
                <label class="validation-label">Maximum File Size (MB):</label>
                <input type="number"
                        class="validation-input"
                        [value]="getValidationValue('maxSize')"
                        (input)="onValidationInputChange($event, 'maxSize')"
                        placeholder="e.g., 5">
              </div>

              <!-- .Action Model******************** -->
            <div class="ActionModel-group">
                <!-- Comment -->
              <div class="menu-item" (click)="handleComment('comment', selectedElement)" title="Add Comment">
                <i class="fas fa-comment"  [ngClass]="{'required': selectedElement.attributes.actions?.comment, 'not-required': !selectedElement.attributes.actions?.comment}"></i>
              </div>

              <!-- Image -->
                <div class="menu-item" (click)="handleImages('image', selectedElement)" title="Add Image">
                <i class="fas fa-image"  [ngClass]="{'required': selectedElement.attributes.actions?.camera, 'not-required': !selectedElement.attributes.actions?.camera}"></i>
              </div>

              <!-- Flag -->
                <!-- <div class="menu-item" (click)="handleFlag('flag', element)" title="Add flag">
                <i class="fas fa-flag"  [ngClass]="{'required': element.attributes.actions?.flag, 'not-required': !element.attributes.actions?.flag}"></i>
              </div> -->

              <!-- FollowUp -->
              <div class="menu-item" (click)="configureMiniForm('followupForm', selectedElement)" title="Add Follow-Up">
                <i class="fas fa-file-signature"  [ngClass]="{'required': selectedElement.attributes.actions?.followupForm?.formId}"></i>
              </div>

            </div>

            <div class="validation-group" style="margin-top: 10px;" *ngIf="selectedElement.attributes.field_Id">
            <div *ngIf="selectedElement.attributes.field_Id">
                <label class="validation-label">Field Name:</label>
                <input type="text" style="border: 1px solid red;"
                        class="validation-input"
                        [value]="selectedElement.attributes.field_Id"
                        (input)="UpateFiledName($event, selectedElement)"
                        placeholder="Enter Placeholder">
                </div>
            </div>



            <button class="btn-advanced-validation" (click)="showAdvancedModalfun(selectedElement)">Advanced</button>
          </div>


          <!-- Model for validation and Style -->
          <div class="modal" *ngIf="showAdvancedModal" >
            <div class="modal-content" style="width: 60vw; height: 84vh; overflow-y: auto;">
              <div class="modal-header">
                <h3><i class="fas fa-code-branch"></i>Advanced</h3>
                <button class="close-btn" (click)="showAdvancedModal = false">
                  <i class="fas fa-times"></i>
                </button>
              </div>
              <div class="modal-body">
                <!-- Modal Content -->
                <div class="properties-header">
                  <button
                    class="tab-btn"
                    [class.active]="activeTab === 'style'"
                    (click)="activeTab = 'style'">
                    Style
                  </button>
                  <button
                    class="tab-btn"
                    [class.active]="activeTab === 'Action'"
                    (click)="activeTab = 'Action'">
                    Action
                  </button>
                </div>

              <div *ngIf="activeTab === 'style'" class = "validation-content">

                  <!-- Font Size -->
                  <div class="validation-group">
                    <label class="validation-label">Font Size:</label>
                    <select class="validation-input"
                            [value]="selectedElement.attributes.style?.['font-size'] || 'medium'"
                            (change)="updateStyle($event, selectedElement)">
                      <option value="">Default</option>
                      <option value="xxx-small">xxx-small</option>
                      <option value="xx-small">xx-small</option>
                      <option value="x-small">x-small</option>
                      <option value="small">small</option>
                      <option value="medium">medium</option>
                      <option value="large">large</option>
                      <option value="x-large">x-large</option>
                      <option value="xx-large">xx-large</option>
                      <option value="xxx-large">xxx-large</option>
                    </select>
                  </div>

                  <!-- Font Bold, italic, underline -->
                  <div class="validation-group font-style-buttons">
                  <label class="validation-label">Font Style:</label>
                  <div class="style-icons">
                    <button type="button"
                            [class.active]="selectedElement.attributes.style?.['font-weight'] === 'bold'"
                            (click)="toggleStyle('font-weight', 'bold', selectedElement)">
                      <i class="fas fa-bold"></i>
                    </button>

                    <button type="button"
                            [class.active]="selectedElement.attributes.style?.['font-style'] === 'italic'"
                            (click)="toggleStyle('font-style', 'italic', selectedElement)">
                      <i class="fas fa-italic"></i>
                    </button>

                    <button type="button"
                            [class.active]="selectedElement.attributes.style?.['text-decoration'] === 'underline'"
                            (click)="toggleStyle('text-decoration', 'underline', selectedElement)">
                      <i class="fas fa-underline"></i>
                    </button>
                  </div>
                </div>

                <!-- Font Color / Backgroup Color -->
                <div class="validation-group" style="display: flex; gap: 5.5vw;">
                  <div>
                  <label class="validation-label">Font Color:</label>
                  <input type="color"
                        [value]="selectedElement.attributes.style?.['color'] || '#c0c0c0'"
                        (input)="updateStyleValue('color', $event, selectedElement)"></div>
                  <div>
                  <label class="validation-label">Background Color:</label>
                        <input type="color"
                              [value]="selectedElement.attributes.style?.['background-color'] || '#ffffff'"
                              (input)="updateStyleValue('background-color', $event, selectedElement)"></div>
                </div>

                <!-- TExt alignment -->
                  <div class="validation-group font-style-buttons">
                  <label class="validation-label">Text Alignment:</label>
                  <div class="style-icons">
                    <button type="button"
                            [class.active]="selectedElement.attributes.style?.['text-align'] === 'left'"
                            (click)="setTextAlign('left', selectedElement)">
                        <i class="fas fa-align-left"></i>
                    </button>

                    <button type="button"
                            [class.active]="selectedElement.attributes.style?.['text-align'] === 'center'"
                          (click)="setTextAlign('center', selectedElement)">
                          <i class="fas fa-align-center"></i>
                    </button>

                    <button type="button"
                            [class.active]="selectedElement.attributes.style?.['text-align'] === 'right'"
                            (click)="setTextAlign('right', selectedElement)">
                          <i class="fas fa-align-right"></i>
                    </button>
                  </div>
                </div>


                </div>

              <div *ngIf="activeTab === 'Action'" class = "validation-content">

                  <!--Apply here for  Conditon for filed -->
              <div class="validation-group">

                <!-- <div style="margin-bottom: 10px;">
                  <label><strong>Action Validation:</strong></label>
                  <select [(ngModel)]="selectedElement.conditionalFieldGroup!.type">
                    <option value="visibility">Show/Hide Field</option>
                    <option value="required">Make Field Required</option>
                  </select>
                </div> -->


                <div class="logic-toggle sub-Header" *ngIf="selectedElement.conditionalFieldGroup?.conditions">
                  <div class="">
                    <label>
                      <input type="radio" [(ngModel)]="selectedElement.conditionalFieldGroup!.logic" value="AND" /> And
                    </label>
                    <label>
                      <input type="radio" [(ngModel)]="selectedElement.conditionalFieldGroup!.logic" value="OR" /> Or
                    </label>
                  </div>

                  <div style="margin-bottom: 10px;">
                    <label><strong>Action Validation:</strong></label>
                    <select [(ngModel)]="selectedElement.conditionalFieldGroup!.type">
                      <option value="" disabled selected
                      [ngClass]="{ 'placeholder-style': selectedElement.conditionalFieldGroup!.type === '' }">Select What Action Performed</option>
                      <option value="visibility">Show/Hide Field</option>
                      <option value="required">Make Field Required</option>
                    </select>
                  </div>
                </div>

                  <!-- Preview -->
                  <div class="condition-preview" *ngIf="selectedElement.conditionalFieldGroup?.conditions!.length > 0">
                      <div class="preview-header">
                        <i class="fas fa-eye"></i> Preview
                      </div>
                      <div class="preview-text">
                        <span *ngIf="selectedElement.conditionalFieldGroup?.type === 'required'">
                          Make it required When
                        </span>
                        <span *ngIf="selectedElement.conditionalFieldGroup?.type === 'visibility'">
                          Show this field when
                        </span>
                        <span *ngFor="let cond of selectedElement.conditionalFieldGroup?.conditions; let i = index">
                          <strong>{{ cond.field }}</strong>
                          <span class="operator">{{ cond.operator === '==' ? 'equals' : 'does not equal' }}</span>
                          <strong>"{{ cond.value }}"</strong>
                          {{i < selectedElement.conditionalFieldGroup?.conditions!.length - 1 ? selectedElement.conditionalFieldGroup?.logic === 'AND' ? 'and' : 'or' : ''}}
                        </span>

                      </div>
                    </div>


                  <!-- Conditions List -->
                  <div class="condition-row" *ngFor="let cond of selectedElement.conditionalFieldGroup?.conditions; let i = index" >

                    <div style="display: flex; gap: 1vw;">
                    <select class="Condition-input" [(ngModel)]="cond.field">
                        <option value="">Select Field</option>
                        <option *ngFor="let field of availableFields" [value]="field.fieldName">{{ field.label }}</option>
                      </select>

                      <select class="Condition-input" [(ngModel)]="cond.operator">
                        <option value="==">is equal to</option>
                        <option value="!=">is not equal to</option>
                      </select>

                      <input class="Condition-input" [(ngModel)]="cond.value" placeholder="Enter value" />
                    </div>

                      <!-- Remove condition -->
                      <button style="color: rgb(179, 3, 3);" (click)="removeCondition(i, selectedElement)" title="Remove Condition" ><i class="fas fa-trash"></i></button>

                    </div>

                    <!-- Add condition -->
                    <button class="btn-advanced-validation" (click)="addCondition(selectedElement)">+ Add Condition</button>
              </div>


              </div>

            </div>
            <div class="modal-validation-footer">
              <button class="validation-Submit-btn" (click)="saveConditions(selectedElement)">Done</button>
            </div>
          </div>
          </div>
        </div>
      </div>

</div>
  <!-- Create Form Modal -->
  <div class="modal" *ngIf="showCreateFormModal">
    <div class="modal-content">
      <div class="modal-header">
        <h3>Create New Form</h3>
        <button class="close-btn" (click)="showCreateFormModal = false">
          <i class="fas fa-times"></i>
        </button>
      </div>
      <div class="modal-body">
        <form [formGroup]="createFormForm" (ngSubmit)="createNewForm()">
          <div class="form-group">
            <label for="newFormName">Form Name</label>
            <input type="text" id="newFormName" formControlName="formName" placeholder="Enter form name">
          </div>
          <div class="form-group">
            <label for="newUserName">User Name</label>
            <input type="text" id="newUserName" formControlName="userName" placeholder="Enter your username">
          </div>

          <div class="modal-actions">
            <button type="button" class="btn-secondary" (click)="showCreateFormModal = false">Cancel</button>
            <button type="submit" class="btn-primary">Create Form</button>
          </div>
        </form>
      </div>
    </div>
  </div>

  <!-- Add Element Type Modal -->
  <div class="modal" *ngIf="showElementSelection">
    <div class="modal-content element-type-modal">
      <div class="modal-header">
        <h3>Create New Item Type</h3>
        <button class="close-btn" (click)="hideElementSelectionSidebar()">
          <i class="fas fa-times"></i>
        </button>
      </div>
      <div class="modal-body element-type-list" style="padding: 0;">
        <div class="element-type-item">
          <div class="element-type-header" (click)="addTextFieldElement()">
            <div class="element-type-icon">
              <i class="fas fa-font"></i>
            </div>
            <div class="element-type-name">Text</div>
          </div>
        </div>
        <div class="element-type-item">
          <div class="element-type-header" (click)="addEmailFieldElement()">
            <div class="element-type-icon">
              <i class="fas fa-envelope"></i>
            </div>
            <div class="element-type-name">Email</div>
          </div>
        </div>
        <div class="element-type-item">
          <div class="element-type-header" (click)="addPhoneNumberFieldElement()">
            <div class="element-type-icon">
              <i class="fas fa-hashtag"></i>
            </div>
            <div class="element-type-name">Phone Number</div>
          </div>
        </div>
        <div class="element-type-item">
          <div class="element-type-header" (click)="addNumberFieldElement()">
            <div class="element-type-icon">
              <i class="fas fa-hashtag"></i>
            </div>
            <div class="element-type-name">Number</div>
          </div>
        </div>
        <div class="element-type-item">
          <div class="element-type-header" (click)="toggleDropdownLists()">
            <div class="element-type-icon">
              <i class="fas fa-check-square"></i>
            </div>
            <div class="element-type-name">Drop Down</div>
          </div>
          <div class="dropdown-lists" *ngIf="showDropdownLists">
            <div class="dropdown-list-header">Select a list:</div>
            <div class="dropdown-search">
              <input type="text"
                     [(ngModel)]="listSearchTerm"
                     (input)="filterLists()"
                     placeholder="Search lists..."
                     class="dropdown-search-input">
            </div>
            <div class="dropdown-list-items">
              <div *ngIf="this.formLists.length === 0" class="no-lists-message">
                No lists available. Please create lists first.
              </div>
              <div *ngIf="this.formLists.length > 0 && filteredFormLists.length === 0" class="no-lists-message">
                No matching lists found.
              </div>
              <div *ngFor="let list of filteredFormLists" class="dropdown-list-item" (click)="selectFormList(list)">
                {{ list.auditHistory.listName }}
              </div>
            </div>
          </div>
        </div>
        <div class="element-type-item">
          <div class="element-type-header" (click)="addUserList()">
            <div class="element-type-icon">
              <i class="fas fa-align-left"></i>
            </div>
            <div class="element-type-name">Select user</div>
          </div>
        </div>
        <div class="element-type-item">
          <div class="element-type-header" (click)="addLocationElement()">
            <div class="element-type-icon">
              <i class="fas fa-align-left"></i>
            </div>
            <div class="element-type-name">Location</div>
          </div>
        </div>
        <div class="element-type-item">
          <div class="element-type-header" (click)="addTextAreaFieldElement()">
            <div class="element-type-icon">
              <i class="fas fa-align-justify"></i>
            </div>
            <div class="element-type-name">Text Area</div>
          </div>
        </div>
        <div class="element-type-item">
          <div class="element-type-header" (click)="addImageElement()">
            <div class="element-type-icon">
              <i class="fas fa-info-circle"></i>
            </div>
            <div class="element-type-name">Image</div>
          </div>
        </div>
        <!-- ********************** -->
         <!-- Guide Image Element -->
         <div class="element-type-item">
          <div class="element-type-header" (click)="toggleGuideImageUpload($event)" >

            <div class="element-type-icon">
              <i class="fas fa-image"></i>
            </div>
            <div class="element-type-name">Image for Guide</div>
          </div>
          <div class="element-type-url-input" *ngIf="showGuideImageUpload">
            <input type="file" accept=".jpg, .jpeg, .png" (change)="onGuideImageSelect($event)">
            <div *ngIf="guideImageUrl" class="guide-image-preview">
              <img [src]="guideImageUrl" alt="Guide Image Preview" style="max-width: 100%; max-height: 150px;">
            </div>
            <button class="btn-add-url" (click)="addGuideImageElement()">Done</button>
          </div>
        </div>
        <!--*********************  -->
        <div class="element-type-item">
          <div class="element-type-header" (click)="addDateFieldElement()">
            <div class="element-type-icon">
              <i class="fas fa-calendar-alt"></i>
            </div>
            <div class="element-type-name">Select Date</div>
          </div>
        </div>
        <div class="element-type-item">
          <div class="element-type-header" (click)="addTimeFieldElement()">
            <div class="element-type-icon">
              <i class="fas fa-calendar-alt"></i>
            </div>
            <div class="element-type-name">Select Time</div>
          </div>
        </div>
        <div class="element-type-item">
          <div class="element-type-header" (click)="addSignatureFieldElement()">
            <div class="element-type-icon">
              <i class="fas fa-info-circle"></i>
            </div>
            <div class="element-type-name">Signature</div>
          </div>
        </div>
        <div class="element-type-item">
          <div class="element-type-header" (click)="addPDFFieldElement()">
            <div class="element-type-icon">
              <i class="fas fa-info-circle"></i>
            </div>
            <div class="element-type-name">Insert PDF</div>
          </div>
        </div>
         <div class="element-type-item">
          <div class="element-type-header" (click)="togglePdfUpload($event)" >

            <div class="element-type-icon">
              <i class="fas fa-image"></i>
            </div>
            <div class="element-type-name">View PDF</div>
          </div>
          <div class="element-type-url-input" *ngIf="showPdfUpload">
            <input type="file" accept="application/pdf" (change)="onPdfSelect($event)">
            <div *ngIf="guideImageUrl" class="guide-image-preview">
              <img [src]="guideImageUrl" alt="Guide Image Preview" style="max-width: 100%; max-height: 150px;">
            </div>
            <button class="btn-add-url" (click)="AddviewPDFFieldElement()">Done</button>
          </div>
        </div>
        <div class="element-type-item">
          <div class="element-type-header" (click)="addQRScannerElement()">
            <div class="element-type-icon">
              <i class="fas fa-info-circle"></i>
            </div>
            <div class="element-type-name">QR Scanner</div>
          </div>
        </div>
        <div class="element-type-item">
          <div class="element-type-header" (click)="toggleLinkUrlInput($event)">
            <div class="element-type-icon">
              <i class="fas fa-link"></i>
            </div>
            <div class="element-type-name">Add Link</div>
          </div>
          <div class="element-type-url-input" *ngIf="showLinkUrlInput">
            <input type="text" [(ngModel)]="linkUrl" placeholder="Enter URL (e.g., https://example.com)">
            <button class="btn-add-url" (click)="addLinkElement()">Done</button>
          </div>
        </div>
      </div>
    </div>
  </div>

<!-- Mini Form Selector Modal -->
  <div *ngIf="showMiniFormSelectorModal" class="mini-form-modal-overlay">
    <div class="mini-form-modal">
      <div class="mini-modal-header">
        <span class="modal-title">
          <i class="fas fa-file-signature"></i>
          Select Follow-up Form
        </span>
        <button class="modal-close-btn" (click)="closeFollowUpFormSelector()">
          <i class="fas fa-times"></i>
        </button>
      </div>

      <div class="mini-modal-body">
        <label class="select-label">Choose a form:</label>
        <div class="select-wrapper">
          <select [(ngModel)]="selectedFollowUpFormId" class="form-select-dropdown">
            <option value="" disabled selected>-- Select a form --</option>
            <option *ngFor="let form of this.formsListforBuilder" [value]="form.id">
              {{ form.auditHistory.formName }}
            </option>
          </select>
          <i class="fas fa-chevron-down select-arrow"></i>
        </div>
      </div>

      <div class="mini-modal-footer">
        <button class="btn btn-save" (click)="applyFollowUpForm()" [disabled]="!selectedFollowUpFormId">
          <i class="fas fa-check"></i>
          Save
        </button>
        <button class="btn btn-cancel" (click)="closeFollowUpFormSelector()">
          <i class="fas fa-times"></i>
          Cancel
        </button>
      </div>
    </div>
  </div>

    </ng-container>
  </div>
</div>
