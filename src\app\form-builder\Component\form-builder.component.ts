import { ShareService } from './../../SharedData/share-services.service';
import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { CoreDataService } from '../../core-data.service';
import { FormJson, FormSection, FormComponent, DropdownData, ConditionalFieldGroup } from '../../home/<USER>/model';
import { Router } from '@angular/router';
import { HttpClient } from '@angular/common/http';
import { forkJoin } from 'rxjs';

@Component({
  selector: 'app-form-builder',
  templateUrl:'./../Template/form-builder.component.html',
  styleUrl: './../Styles/form-builder.component.css'
})
export class FormBuilderComponent implements OnInit {
  // Form builder form
  formBuilderForm: FormGroup;

  // Form data
  formData: FormJson = {
    auditHistory:{
      formName: '',
      userName: '',
      location: '',
    },
    component:[]
  };

  // Properties for Field
  activeTab: 'style' | 'Action' = 'style';
  isSidebarCollapsed: boolean = false;

  // Create form modal
  showAdvancedModal: boolean = false; //for advanceValidationModel
  showCreateFormModal: boolean = false;
  createFormForm: FormGroup;

  // Track the currently selected section for adding elements
  selectedSection: FormSection | null = null;

  // Flag to show/hide the element selection sidebar
  showElementSelection: boolean = false;

  // Flag to show URL input in the element selection modal
  showLinkUrlInput: boolean = false;

  // Flag to show dropdown lists
  showDropdownLists: boolean = false;

  // Available form lists for dropdown
  formLists: DropdownData[] = [];

  // Flag to show guide image upload
  showGuideImageUpload: boolean = false;

  // Guide image data
  guideImageUrl: string = '';

  // Filtered form lists based on search
  filteredFormLists: DropdownData[] = [];

  // Search term for filtering lists
  listSearchTerm: string = '';

  // Selected form list for dropdown
  selectedFormList: DropdownData | null = null;

  // Flag to show list selector in validation panel
  showListSelector: boolean = false;

  // Form list properties for sidebar layout
  selectedFormId: string | null = null;

  // Reference to the link item element
  linkItemElement: Element | null = null;

  // for Load From Data'
   formsListforBuilder: FormJson[] = [];

  // Available field names for dropdown
  availableFields: { fieldName: string, label: string }[] = [];
  showAdvancedConditionModal: boolean = false;

  // Selected element for validation editing
  selectedElement: FormComponent | null = null;
  selectedElementSection: FormSection | null = null;
  showPdfUpload: boolean = false;

  constructor(
    private coreDataService: CoreDataService,
    private fb: FormBuilder, private route:Router,
    protected shareService:ShareService,
    private http: HttpClient
  ) {
    this.formBuilderForm = this.fb.group({
        formName: ['New Form', Validators.required],
        userName: ['Current User', Validators.required],
    });

    this.createFormForm = this.fb.group({
        formName: ['New Form', Validators.required],
        userName: ['Current User', Validators.required],
        // location: [this.shareService.selectedLocation, Validators.required],
    });
  }

  ngOnInit(): void {
    // Initialize with one empty section
    // this.addSection();
    // add header in DB_DATA
    this.formData.auditHistory.location = this.shareService.selectedLocation;
    this.loadFormData();

    // Subscribe to form value changes
    this.formBuilderForm.valueChanges.subscribe(values => {
      this.updateFormData(values);
    });
  }
  toggleSidebar() {
    this.isSidebarCollapsed = !this.isSidebarCollapsed;
  }
  closeRightSidebar() {
    this.selectedElement = null;
    this.selectedElementSection = null;
  }

  loadFormData(): void {
  forkJoin({
    forms: this.coreDataService.getAllforms(),
    formList: this.coreDataService.getallFormList()
  }).subscribe({
    next: ({ forms, formList }) => {
      this.formsListforBuilder = forms;
      this.formLists = formList;
    },
    error: (err) => {
      console.error('Error loading form data:', err);
    }
  });
}

  // Filter lists based on search term
  filterLists(): void {
    if (!this.listSearchTerm.trim()) {
      this.filteredFormLists = [...this.formLists];
      return;
    }
    console.log(this.filteredFormLists, 'filteredFormLists');
    const searchTerm = this.listSearchTerm.toLowerCase().trim();
    this.filteredFormLists = this.formLists.filter(list =>
      list.auditHistory.listName?.toLowerCase().includes(searchTerm)
    );
  }

  // Create a new form
  createNewForm(): void {
    const formValues = this.createFormForm.value;

    this.formData = {
      auditHistory: {
        formName: formValues.formName,
        userName: formValues.userName,
        location: this.shareService.selectedLocation,
      },
      component: []
    };
    this.formBuilderForm.patchValue({
      formName: formValues.formName,
      userName: formValues.userName,
      location: this.shareService.selectedLocation,
    });

    // Add an initial section
    this.addSection();

    // Set selected form ID to show the form builder
    this.selectedFormId = 'new-form';

    // Close the Form pop-up
    this.showCreateFormModal = false;

    console.log('New form created:', this.formData);
  }
  // Update form data when form values change
  updateFormData(values: any): void {
    this.formData.auditHistory.formName = values.formName;
    this.formData.auditHistory.userName = values.userName;
    // console.log('Form data updated:', this.formData);
  }
  // New section
  addSection(): void {
    const newSection: FormSection = {
      title: 'Rename Section',
      canCollapsed: false, // Default to not collapsible
      isCollapsed: false,
      repeatable: false,  // Default to not repetable
      elements: []
    };
    this.formData.component.push(newSection);
  }

handleConditionToggle(section: FormSection): void {
  this.showAdvancedConditionModal = true;
  this.updateAvailableFields();

}

applyCondition(section: FormSection): void {
  this.showAdvancedConditionModal = false;
  console.log('Condition applied:', section);
}

cancelCondition(): void {
  this.showAdvancedConditionModal = false;
}

// Get all available field names from the current form
updateAvailableFields(): void {
  this.availableFields = [];

  if (this.formData && this.formData.component) {
    this.formData.component.forEach(section => {
      section.elements.forEach(element => {
        if (element.attributes.field_Id && element.attributes.label) {
          this.availableFields.push({
            fieldName: element.attributes.field_Id,
            label: element.attributes.label
          });
        }
      });
    });
  }
}

  updateSectionTitle(section: FormSection, newTitle: string): void {
    section.title = newTitle;
  }

  // Toggle section collapse state
  toggleSectionCollapse(section: FormSection): void {
    if (section.canCollapsed) {
      section.isCollapsed = !section.isCollapsed;
    }
  }

  // Save the form
  saveForm(): void {
    console.log(JSON.stringify(this.formData, null, 2));

    // Here you would typically send the form data to your backend
    this.coreDataService.saveFormTemplate(this.formData).subscribe(response => {
        console.log('Form saved successfully', response);
        this.formData = {
          auditHistory: {
            formName: '',
            userName: '',
            location: '',
          },
          component: []
        };
        this.route.navigate(['form/admin/config']);
        this.selectedFormId = null;
    });
  }

  // Show element selection modal for a specific section
  showElementSelectionSidebar(section: FormSection): void {
    this.selectedSection = section;
    this.showElementSelection = true;
  }

  // Hide element selection modal
  hideElementSelectionSidebar(): void {
    this.showElementSelection = false;
    this.showGuideImageUpload = false;
    this.showLinkUrlInput = false;
    this.showDropdownLists = false;
    this.selectedSection = null;
  }
  // Generate field name based on label
 generateFieldName(type: string): string {

  const baseName = type.trim().toLowerCase();
  let index = 1;
  let fieldName = baseName;

  // 🔍 Flatten all field names from all sections
  const allFieldNames = this.formData.component
    .flatMap(section => section.elements)
    .map(el => el.attributes?.field_Id?.toLowerCase());

  // 📛 Avoid duplicates across entire form
  while (allFieldNames.includes(fieldName.toLowerCase())) {
    index++;
    fieldName = `${baseName}${index}`;
  }

  return fieldName;
}


  // Update field name when label changes
  // updateFieldName(element: FormComponent, newLabel: string, sectionTitle: string): void {
  //   element.attributes.field_Id = this.generateFieldName(newLabel, sectionTitle);
  // }

  // Toggle required status
  toggleRequired(element: FormComponent): void {
    element.attributes.is_required = !element.attributes.is_required;
  }

  hoveredIndex: number | null = null;

  handleFlag(option: string, element: any): void {
    element.attributes.actions.flag = !element.attributes.actions.flag;
  }
  handleComment(option: string, element: any): void {
    element.attributes.actions.comment = !element.attributes.actions.comment;
  }
  handleImages(option: string, element: any): void {
    element.attributes.actions.camera = !element.attributes.actions.camera;
  }

  // Mini Form Selector
  activeFormElement: FormComponent | null = null;
  showMiniFormSelectorModal: boolean = false;
  configureMiniForm(option: string, element: FormComponent): void {
  this.activeFormElement = element;
  this.showMiniFormSelectorModal = true;
}
  selectedFollowUpFormId: string = '';

applyFollowUpForm(): void {
  const selectedForm = this.formsListforBuilder.find(f => f.id === this.selectedFollowUpFormId);
  if (selectedForm && this.activeFormElement) {
    if (this.activeFormElement.attributes.actions) {
      this.activeFormElement.attributes.actions.followupForm = {
        formId: selectedForm.id ?? '',
        formName: selectedForm.auditHistory.formName ?? ''
      };
    }
    this.shareService.showSuccess("Follow-up form linked.");
  }
  this.closeFollowUpFormSelector();
}

closeFollowUpFormSelector(): void {
  this.activeFormElement = null;
  this.selectedFollowUpFormId = '';
  this.showMiniFormSelectorModal = false;
}


  // Toggle multiselect status for dropdown
  toggleMultiselect(element: FormComponent): void {
    if (element.type !== 'Select') return;
    element.multiselect = !element.multiselect;
  }

  // Delete element from section
  deleteElement(section: FormSection, elementIndex: number): void {
    section.elements.splice(elementIndex, 1);
  }

    // Add a text field element to a section
  addTextFieldElement(): void {
    if (!this.selectedSection) return;

    const label = "Label";
    const textFieldElement: FormComponent = {
      type: "text",
      attributes: {
        label: label,
        field_Id: this.generateFieldName('text'),
        is_required: false,
        placeholder_text: "Short Answer",
        show_label: true,
        style: {},
        validations: {
        },
        actions: {
          comment: false,
          camera: false,
          flag: false,
          followupForm:{
            formId: '',
            formName: ''
          }
        }
      }
    };
    this.selectedSection.elements.push(textFieldElement);
    this.hideElementSelectionSidebar();
  }

  // Add an email field element to a section
  addEmailFieldElement(): void {
    if (!this.selectedSection) return;

    const label = "Email";
    const emailFieldElement: FormComponent = {
      type: "text",
      attributes: {
        label: label,
        field_Id: this.generateFieldName('text'),
        is_required: false,
        placeholder_text: "Email",
        show_label: true,
        validations: {
           pattern: "^[a-zA-Z0-9._%+-]+@[a-z0-9.-]+\\.[a-z]{2,4}$"
        },
        style: {},
        actions: {
          comment: false,
          camera: false,
          flag: false,
          followupForm:{
            formId: '',
            formName: ''
          }
        }
      }
    };
    this.selectedSection.elements.push(emailFieldElement);
    this.hideElementSelectionSidebar();
  }

  // Add a number field element to a section
  addPhoneNumberFieldElement(): void {
    if (!this.selectedSection) return;

    const label = "Phone No.";
    const numberFieldElement: FormComponent = {
      type: "text",
      attributes: {
        label: label,
        field_Id: this.generateFieldName('text'),
        is_required: false,
        placeholder_text: "Number",
        show_label: true,
        default_value: "+91",
        style: {},
        validations: {
          minlength: "10",
          maxlength: "13",
          pattern: "^(\\+91)?[0-9]{10}$"
        },
        actions: {
          comment: false,
          camera: false,
          flag: false,
          followupForm:{
            formId: '',
            formName: ''
          }
        }
      }
    };
    this.selectedSection.elements.push(numberFieldElement);
    this.hideElementSelectionSidebar();
  }

  addNumberFieldElement(): void {
    if (!this.selectedSection) return;
    const label = "Number";
    const numberFieldElement: FormComponent = {
      type: "text",
      attributes: {
        label: label,
        field_Id: this.generateFieldName('text'),
        is_required: false,
        placeholder_text: "Number",
        show_label: true,
        style: {},
        validations: {
          pattern: "^s*[0-9]+s*$"
        },
        actions: {
          comment: false,
          camera: false,
          flag: false,
          followupForm:{
            formId: '',
            formName: ''
          }
        }
      }
    }
    this.selectedSection.elements.push(numberFieldElement);
    this.hideElementSelectionSidebar();
  }

  addTextAreaFieldElement(): void {
    if (!this.selectedSection) return;

    const label = "Enter Your Label";
    const textFieldElement: FormComponent = {
      type: "textarea",
      attributes: {
        label: label,
        field_Id: this.generateFieldName('textarea'),
        is_required: false,
        placeholder_text: "Long Answer",
        show_label: true,
        style: {},
        actions: {
          comment: false,
          camera: false,
          flag: false,
          followupForm:{
            formId: '',
            formName: ''
          }
        }
      }
    };
    this.selectedSection.elements.push(textFieldElement);
    this.hideElementSelectionSidebar();
  }

  addLocationElement(): void {
    if (!this.selectedSection) return;

    const label = "Select Location";
    const locationElement: FormComponent = {
      type: "map",
      attributes: {
        label: label,
        field_Id: this.generateFieldName('map'),
        is_required: false,
        show_label: true,
        default_lat: 28.6139,
        default_lng: 77.209,
        style: {},
        actions: {
          comment: false,
          camera: false,
          flag: false,
          followupForm:{
            formId: '',
            formName: ''
          }
        }
      }
    };
    this.selectedSection.elements.push(locationElement);
    this.hideElementSelectionSidebar();
  }

  addDateFieldElement(): void {
    if (!this.selectedSection) return;
    const label = "Select Date";
    const dateFieldElement: FormComponent = {
      type: "date",
      attributes: {
        label: label,
        field_Id: this.generateFieldName('date'),
        is_required: false,
        show_label: true,
        validations: {
                  minDate: "",
                  maxDate: ""
        },
        style: {},
        actions: {
          comment: false,
          camera: false,
          flag: false,
          followupForm:{
            formId: '',
            formName: ''
          }
        }
      }
    }
    this.selectedSection.elements.push(dateFieldElement);
    this.hideElementSelectionSidebar();
  }

  addTimeFieldElement(): void {
    if (!this.selectedSection) return;
    const label = "Select Time";
    const timeFieldElement: FormComponent = {
      type: "time",
      attributes: {
        label: label,
        field_Id: this.generateFieldName('time'),
        is_required: false,
        show_label: true,
        style: {},
        actions: {
          comment: false,
          camera: false,
          flag: false,
          followupForm:{
            formId: '',
            formName: ''
          }
        }
      }
    }
    this.selectedSection.elements.push(timeFieldElement);
    this.hideElementSelectionSidebar();
  }

  addSignatureFieldElement(): void {
    if (!this.selectedSection) return;
    const label = "signature";
    const signatureFieldElement: FormComponent = {
      type: "signature",
      attributes: {
        label: label,
        field_Id: this.generateFieldName('signature'),
        is_required: false,
        show_label: true,
        pen_color: "black",
        style: {},
        actions: {
          comment: false,
          camera: false,
          flag: false,
          followupForm:{
            formId: '',
            formName: ''
          }
        }
      }
    };
    this.selectedSection.elements.push(signatureFieldElement);
    this.hideElementSelectionSidebar();
  }

  addImageElement(): void {
    if (!this.selectedSection) return;
    const label = "Upload Image";
    const imageElement: FormComponent = {
      type: "file",
      attributes: {
        label: label,
        field_Id: this.generateFieldName('file'),
        is_required: false,
        show_label: true,
        style: {},
        placeholder_text: "Upload Your Image",
        validations: {
          pattern: "\\.(jpg|jpeg|png)$",
          maxSize: "5"
        },
        actions: {
          comment: false,
          camera: false,
          flag: false,
          followupForm:{
            formId: '',
            formName: ''
          }
        }
      }
    };
    this.selectedSection.elements.push(imageElement);
    this.hideElementSelectionSidebar();
  }
  // Toggle URL input and scroll to it
  toggleLinkUrlInput(event: Event): void {
    this.showLinkUrlInput = !this.showLinkUrlInput;

    if (this.showLinkUrlInput) {
      // Get the parent element (element-type-item)
      const target = event.currentTarget as HTMLElement;
      const parentElement = target.closest('.element-type-item');

      if (parentElement) {
        // Store reference to the element
        this.linkItemElement = parentElement;

        // Wait for the DOM to update before scrolling
        setTimeout(() => {
          parentElement.scrollIntoView({ behavior: 'smooth', block: 'center' });
        }, 100);
      }
    }
  }
  linkUrl: string = '';
  // Add a link element to the selected section
  addLinkElement(): void {
    if (this.selectedSection) {
      // Use the entered URL or a default if empty
      const url = this.linkUrl.trim() || '';
      const label = "Enter Your Label";
      const newElement: FormComponent = {
        type: 'link',
        attributes: {
          label: label,
          is_required: false,
          show_label: true,
          default_value: '',
          url: url,
          style: {},
          link_text: 'Click here',
          validations: {},
          actions: {
            comment: false,
            camera: false,
            flag: false,
            followupForm:{
            formId: '',
            formName: ''
          }
          }
        }
      };
      this.selectedSection.elements.push(newElement);
      this.hideElementSelectionSidebar();
      this.linkUrl = '';
    }
  }

  // Toggle dropdown lists visibility
  toggleDropdownLists(): void {

    this.showDropdownLists = !this.showDropdownLists;
    if (this.showDropdownLists) {
      // Reset search when showing dropdown lists
      this.listSearchTerm = '';
      this.filteredFormLists = [...this.formLists];
    }
  }

  // Select a form list for dropdown
  selectFormList(list: DropdownData): void {
    this.selectedFormList = list;
    this.addDropdownElement(list);
  }

  // Get list name by ID for display
  getListNameById(listId: string): string {
    const list = this.formLists.find(l => l.id === listId);
    return list ? list.auditHistory.listName || 'Unnamed List' : 'Unknown List';
  }

  // Select a list for an existing element
  toggleList(){
    this.showListSelector = !this.showListSelector;
    if(this.showListSelector){
      this.filteredFormLists = [...this.formLists];
    }
  }

  selectListForElement(list: DropdownData, element: FormComponent): void {
    if (element.type === 'Select') {
      (element.attributes as any).dataListId = list.id ?? '';
      this.showListSelector = false;
      // Reset search term and filtered lists
      this.listSearchTerm = '';
      this.filteredFormLists = [...this.formLists];
    }
  }

  // Add a dropdown element with the selected form list
  addDropdownElement(list: DropdownData): void {
    if (!this.selectedSection) return;

    const label = "Enter a Label";
    const dropdownElement: FormComponent = {
      type: "Select",
      multiselect: false,
      attributes: {
        label: label,
        field_Id: this.generateFieldName('Select'),
        is_required: false,
        placeholder_text: "Select One",
        show_label: true,
        style: {},
        dataListId: list.id ?? '', // Ensure dataListId is always a string
        actions: {
          comment: false,
          camera: false,
          flag: false,
          followupForm:{
            formId: '',
            formName: ''
          }
        },
        validations: {}
      }
    };
    this.selectedSection.elements.push(dropdownElement);
    this.hideElementSelectionSidebar();
  }

  addUserList():void{
    if (!this.selectedSection) return;

    // Fetch users from API and convert to DropdownData format
    this.coreDataService.GetUsers().subscribe({
      next: (users: any[]) => {
        // Check if users array is empty
        if (!users || users.length === 0) {
          this.shareService.showError('No users found in the system.');
          return;
        }
        // Convert users to DropdownData format
         const usersDropdownData = this.convertUsersToDropdownData(users);

        const label = "Select User";
        const dropdownElement: FormComponent = {
          type: "Select",
          multiselect: false,
          attributes: {
            label: label,
            field_Id: this.generateFieldName('Select'),
            is_required: false,
            placeholder_text: "Select Users",
            show_label: true,
            style: {},
            dataListId: '', // Use a fixed ID for the user list
            dataSource: usersDropdownData, // Set the converted user data
            actions: {
              comment: false,
              camera: false,
              flag: false,
              followupForm:{
            formId: '',
            formName: ''
          }
            }
          }
        };

        // Add the dropdown element to the selected section
        this.selectedSection?.elements.push(dropdownElement);
        this.hideElementSelectionSidebar();

        // Show success message
        this.shareService.showSuccess('User dropdown added successfully!');
      },
      error: (error) => {
        console.error('Error fetching users:', error);
        this.shareService.showError('Failed to fetch users. Please try again.');
      }
    });
  }

  // convert User to List
convertUsersToDropdownData(users: any[]): DropdownData {
  return {
    auditHistory: {},
    list: [
      {
        items: users.map(user => {
          const userName = user.details?.name ||
                           user.name ||
                           user.firstName ||
                           user.username ||
                           `User ${user.id}`;

          return { value: userName };
        })
      }
    ]
  };
}


   // Toggle guide image upload input
   toggleGuideImageUpload(event: Event): void {
    this.showGuideImageUpload = !this.showGuideImageUpload;

    if (this.showGuideImageUpload) {
      // Get the parent element (element-type-item)
      const target = event.currentTarget as HTMLElement;
      const parentElement = target.closest('.element-type-item');

      if (parentElement) {
        // Wait for the DOM to update before scrolling
        setTimeout(() => {
          parentElement.scrollIntoView({ behavior: 'smooth', block: 'center' });
        }, 100);
      }
    }
  }
  imagePath: string = '';
  // Handle guide image upload
  onGuideImageSelect(event: Event): void {
    const input = event.target as HTMLInputElement;
    if (input?.files?.length) {
      const file = input.files[0];

      // Validate file type
      const allowedExtensions = new RegExp("\\.(jpg|jpeg|png)$", "i");
      if (!allowedExtensions.test(file.name)) {
        console.error('Invalid file type. Only JPG, JPEG, and PNG are allowed.');
        return;
      }

      // Validate file size (max 5MB)
      const maxSizeMB = 5;
      if (file.size / 1024 / 1024 > maxSizeMB) {
        console.error(`File size must be less than ${maxSizeMB} MB.`);
        return;
      }

      // Preview the uploaded image
      this.guideImageUrl = URL.createObjectURL(file);

      // Upload the image to the server
      this.coreDataService.saveImage(file).subscribe({
        next: (res) => {
          this.imagePath = res.imagePath; // e.g., "uploads/image123.png"
          console.log('Image uploaded successfully:', res.imagePath);
        },
        error: (err) => {
          console.error('Image upload failed:', err);
        }
      });
    }
  }

  // Add guide image element with the uploaded image
  addGuideImageElement(): void {
    if (!this.selectedSection) return;

    const label = "For Your Guidance";
    const imageElement: FormComponent = {
      type: "image",
      attributes: {
        label: label,
        image_url: this.imagePath || "",
        show_label: true,
        style: {},
        alt_text: "Guide to fill the form",
        actions: {
          comment: false,
          camera: false,
          flag: false,
          followupForm:{
            formId: '',
            formName: ''
          }
        }
      }
    };
    this.selectedSection.elements.push(imageElement);
    this.hideElementSelectionSidebar();
  }

  addQRScannerElement(){
    if (!this.selectedSection) return;
    const label = "QR Scanner";
    const qrScannerElement: FormComponent = {
      type: "qrscanner",
      attributes: {
        label: label,
        field_Id: this.generateFieldName('qrscanner'),
        is_required: false,
        show_label: true,
        style: {},
        actions: {
          comment: false,
          camera: false,
          flag: false,
          followupForm:{
            formId: '',
            formName: ''
          }
        }
      }
    };
    this.selectedSection.elements.push(qrScannerElement);
    this.hideElementSelectionSidebar();
  }

  addPDFFieldElement(): void {
    if (!this.selectedSection) return;
    const label = "Upload PDF";
    const pdfElement: FormComponent = {
      type: "PDFfile",
      attributes: {
        label: label,
        field_Id: this.generateFieldName('PDFfile'),
        is_required: false,
        show_label: true,
        style: {},
        placeholder_text: "Upload Your PDF",
        validations: {
          maxSize: "5"
        },
        actions: {
          comment: false,
          camera: false,
          flag: false,
          followupForm:{
            formId: '',
            formName: ''
          }
        }
      }
    };
    this.selectedSection.elements.push(pdfElement);
    this.hideElementSelectionSidebar();
  }

  togglePdfUpload(event: Event):void{
    this.showPdfUpload = !this.showPdfUpload;
    if (this.showPdfUpload) {
      // Get the parent element (element-type-item)
      const target = event.currentTarget as HTMLElement;
      const parentElement = target.closest('.element-type-item');

      if (parentElement) {
        // Wait for the DOM to update before scrolling
        setTimeout(() => {
          parentElement.scrollIntoView({ behavior: 'smooth', block: 'center' });
        }, 100);
      }
    }
  }

  pdfComponent: string = '';
  onPdfSelect(event: Event){
      const input = event.target as HTMLInputElement;

  if (input.files && input.files.length > 0) {
    const file = input.files[0];

    const reader = new FileReader();

    reader.onload = () => {
      const base64String = (reader.result as string);

      // Convert base64 to Blob structure
        this.pdfComponent = base64String;
    };

    reader.readAsDataURL(file); // This reads the PDF as base64 string
  }
  }

  AddviewPDFFieldElement(): void {
    if (!this.selectedSection) return;
    const label = "View PDF";
    const pdfElement: FormComponent = {
      type: "pdfviewer",
      attributes: {
        label: label,
        is_required: false,
        pdf_url: this.pdfComponent ||"",
        show_label: true,
        style: {},
        actions: {
          comment: false,
          camera: false,
          flag: false,
          followupForm:{
            formId: '',
            formName: '',
          }
        }
      }
    };
    this.selectedSection.elements.push(pdfElement);
    this.hideElementSelectionSidebar();
  }

  // Select a form from the sidebar
  selectForm(form: FormJson): void {
    this.selectedFormId = form.id || null;
    this.loadFormForEditing(form);
  }

  // Load an existing form for editing
  loadFormForEditing(form: FormJson): void {
    this.formData = { ...form }; // Create a copy to avoid modifying the original
    // Update the form builder form with the loaded data
    this.formBuilderForm.patchValue({
      formName: form.auditHistory.formName || 'Untitled Form',
      userName: form.auditHistory.userName || 'Unknown User',
    });

  }

  // Start creating a new form
  startNewForm(): void {
    this.showCreateFormModal = true;
  }

  // Select element for validation editing
  selectElementForValidation(element: FormComponent, section: FormSection): void {
    this.selectedElement = element;
    this.selectedElementSection = section;
  }

  // Clear selected element
  clearSelectedElement(): void {
    this.selectedElement = null;
    this.selectedElementSection = null;
    this.showListSelector = false;
  }

  // Update validation for selected element
  updateValidation(validationType: string, value: any): void {
    if (!this.selectedElement) return;

    if (!this.selectedElement.attributes.validations) {
      this.selectedElement.attributes.validations = {};
    }

    if (value === '' || value === null || value === undefined) {
      delete this.selectedElement.attributes.validations[validationType];
    } else {
      this.selectedElement.attributes.validations[validationType] = value;
    }
  }
  updatelabel(event:Event, element: FormComponent){ //Updating the label
    const target = event.target as HTMLInputElement;
    element.attributes.label = target.value;
  }
  UpatePlaceholder(event:Event, element: FormComponent){ //Updating the placeholder
    const target = event.target as HTMLInputElement;
    if ('placeholder_text' in element.attributes) {
      element.attributes.placeholder_text = target.value;
    }
  }
  UpateFiledName(event:Event, element: FormComponent){ //Updating the field name
    const target = event.target as HTMLInputElement;
    element.attributes.field_Id = target.value;
  }

  // Handle multiselect toggle
  onMultiselectToggle(event: Event): void {
  const isChecked = (event.target as HTMLInputElement).checked;
    if (this.selectedElement?.type === 'Select') {
      this.selectedElement.multiselect = isChecked;
      this.selectedElement.attributes.placeholder_text = isChecked ? 'Select Multiple' : 'Select One';
    }
  }

  // Handle input events for validation updates
  onValidationInputChange(event: Event, validationType: string): void {
    const target = event.target as HTMLInputElement;
    this.updateValidation(validationType, target.value);
  }

  // Handle checkbox events for validation updates
  onValidationCheckboxChange(event: Event, validationType: string): void {
    const target = event.target as HTMLInputElement;
    this.updateValidation(validationType, target.checked ? 'true' : '');
  }

  // Get validation value for selected element
  getValidationValue(validationType: string): any {
    if (!this.selectedElement || !this.selectedElement.attributes.validations) {
      return '';
    }
    return this.selectedElement.attributes.validations[validationType] || '';
  }

  // Check if validation type is applicable for current element type
  isValidationApplicable(validationType: string): boolean {
    if (!this.selectedElement) return false;

    const elementType = this.selectedElement.type;
    const elementLabel = this.selectedElement.attributes.label?.toLowerCase() || '';

    // Check if this is an email field (either by type or label)
    const isEmailField = elementLabel.includes('email');

    switch (validationType) {
      case 'minlength':
      case 'maxlength':
        // Email fields should not show length validation
        if (isEmailField) return false;
        return ['text', 'textarea'].includes(elementType);
      case 'min':
      case 'max':
        return elementLabel.includes('number') ;
      case 'minDate':
      case 'maxDate':
      case 'noFuture':
      case 'noPast':
        return elementType === 'date';
      case 'maxSize':
        return elementType === 'file';
      default:
        return true;
    }
  }

  // Style Properties******************************
updateStyle(event: Event, element: FormComponent): void {
    if (!element.attributes.style) {
      element.attributes.style = {};
    }
    const target = event.target as HTMLInputElement | null;
    if (target && target.value) {
      element.attributes.style['font-size'] = target.value;
    }
  }
toggleStyle(styleKey: string, value: string, element: FormComponent): void {
  if (!element?.attributes.style) {
    element.attributes.style = {};
  }

  const currentValue = element.attributes.style[styleKey];

  if (currentValue === value) {
    delete element.attributes.style[styleKey]; // Remove style if toggled off
  } else {
    element.attributes.style[styleKey] = value;
  }
}
updateStyleValue(styleKey: string, event: Event, element: FormComponent): void {
  if (!element?.attributes.style) {
    element.attributes.style = {};
  }
  const target = event.target as HTMLInputElement | null;
  if (target) {
    element.attributes.style[styleKey] = target.value;
  }
}

setTextAlign(align: 'left' | 'center' | 'right', element: FormComponent): void {
  if (!element?.attributes.style) {
    element.attributes.style = {};
  }
  element.attributes.style['text-align'] = align;
}

// Validation******************
showAdvancedModalfun(element: FormComponent):void{
  this.showAdvancedModal = true;
  // this.tempCondition = {
  //   field: element.conditionalfiled?.field || '',
  //   operator: element.conditionalfiled?.operator || '==',
  //   value: element.conditionalfiled?.value || ''
  // };
  this.updateAvailableFields();
}

saveConditions(element: FormComponent): void {
  console.log('Saved:', element.conditionalFieldGroup);
  this.showAdvancedModal = false;
  // Save it to your component or backend
}
removeCondition(index: number, element: FormComponent): void { //remove Condiiton from Field Level function
  if (element.conditionalFieldGroup && element.conditionalFieldGroup.conditions) {
    element.conditionalFieldGroup.conditions.splice(index, 1);
  }
}
removeConditionForSection(index: number, section: FormSection): void{  //remove Condiiton from Section function
  if(section.conditionalAtSection && section.conditionalAtSection.conditions){
    section.conditionalAtSection.conditions.splice(index, 1);
  }
}
addCondition(element: FormComponent): void {
  if (!element.conditionalFieldGroup) {
    element.conditionalFieldGroup = {type: '', logic: '', conditions: [] };
  }
  element.conditionalFieldGroup.conditions.push({
    field: '',
    operator: '==',
    valueType: 'Value',
    value: ''
  });
}
addConditionForSection(section:FormSection):void{
  if(!section.conditionalAtSection){
    section.conditionalAtSection = {type: '',logic: '', conditions:[]};
  }
  section.conditionalAtSection.conditions.push({
    field: '',
    operator: '==',
    valueType: 'Value',
    value: ''
  })
}

// Sub Navigation Bar Methods
previewForm(): void {
  console.log('Preview form:', this.formData);
  // TODO: Implement form preview functionality
  this.shareService.showInfo('Form preview functionality coming soon!');
}


exportForm(): void {
  if (!this.formData) return;

  console.log('Exporting form:', this.formData);

  // Create a downloadable JSON file
  const dataStr = JSON.stringify(this.formData, null, 2);
  const dataBlob = new Blob([dataStr], { type: 'application/json' });
  const url = URL.createObjectURL(dataBlob);

  // Create download link
  const link = document.createElement('a');
  link.href = url;
  link.download = `${this.formData.auditHistory.formName || 'form'}.json`;
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
  URL.revokeObjectURL(url);

  this.shareService.showSuccess('Form exported successfully!');
}


}

