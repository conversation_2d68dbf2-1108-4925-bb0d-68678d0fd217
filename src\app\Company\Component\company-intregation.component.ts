import { CoreDataService } from './../../core-data.service';
import { ShareService } from './../../SharedData/share-services.service';
import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { company } from '../Models/Comany-Model';

@Component({
  selector: 'app-company-intregation',
  templateUrl: './../Template/company-intregation.component.html',
  styleUrl: './../Style/company-intregation.component.css'
})
export class CompanyIntregationComponent implements OnInit {
constructor(
  private shareService: ShareService,
  private coreData: CoreDataService,
  private fb: FormBuilder
){}
asurid!: string;
tenantId!: string;
clientId!: string;
selectedSection: string = 'data-source';
isEditing = false;

// Company form properties
companyForm!: FormGroup;
isFormDirty: boolean = false;

originalCompanyValues: company = {
  businessName: 'Form Control',
  businessRegistrationNumber: '2007191530003',
  EmailID: '<EMAIL>',
  phoneNumber: '6388342176',
  country: 'India',
  state: 'Uttar Pradesh',
  city: 'Kanpur',
  address: 'B/Y L.I.G. 29 ',
  postalCode: 208027
};

ngOnInit(): void {
  this.asurid = localStorage.getItem('msal_asur_id')!;
  this.clientId = localStorage.getItem('msal_client_id')!;
  this.tenantId = localStorage.getItem('msal_tenant_id')!;

  this.initializeCompanyForm();
}

initializeCompanyForm(): void {
  this.companyForm = this.fb.group({
    businessName: [this.originalCompanyValues.businessName, [Validators.required, Validators.minLength(2)]],
    businessRegistrationNumber: [this.originalCompanyValues.businessRegistrationNumber, [Validators.required]],
    EmailID: [this.originalCompanyValues.EmailID, [Validators.required, Validators.email]],
    phoneNumber: [this.originalCompanyValues.phoneNumber, [Validators.required, Validators.pattern(/^\+?[1-9]\d{1,14}$/)]],
    country: [this.originalCompanyValues.country, [Validators.required]],
    state: [this.originalCompanyValues.state, [Validators.required]],
    city: [this.originalCompanyValues.city, [Validators.required]],
    address: [this.originalCompanyValues.address, [Validators.required]],
    postalCode: [this.originalCompanyValues.postalCode, [Validators.required, Validators.min(1)]]
  });

  // Track form changes
  this.companyForm.valueChanges.subscribe(() => {
    this.isFormDirty = this.companyForm.dirty;
  });
}

save() {
  this.isEditing = false;
  // Save to backend/localStorage here
  console.log('Saved:', this.clientId, this.tenantId);
  this.coreData.saveAsureID(this.clientId, this.tenantId, this.asurid).subscribe({
    next: (response) => {
      window.location.reload();
    }
  });
}

// Company form methods
selectSection(section: string): void {
  this.selectedSection = section;
}

isFieldInvalid(fieldName: string): boolean {
  const field = this.companyForm.get(fieldName);
  return !!(field && field.invalid && (field.dirty || field.touched));
}

getFieldErrorMessage(fieldName: string): string {
  const field = this.companyForm.get(fieldName);
  if (field && field.errors) {
    if (field.errors['required']) {
      return `${fieldName} is required`;
    }
    if (field.errors['email']) {
      return 'Please enter a valid email address';
    }
    if (field.errors['minlength']) {
      return `${fieldName} must be at least ${field.errors['minlength'].requiredLength} characters`;
    }
    if (field.errors['pattern']) {
      return 'Please enter a valid phone number';
    }
    if (field.errors['min']) {
      return 'Postal code must be greater than 0';
    }
  }
  return '';
}

onCompanySubmit(): void {
  this.companyForm.markAllAsTouched();

  if (this.companyForm.valid) {
    const companyData = this.companyForm.value;

    // Here you would typically save to backend
    console.log('Company data to save:', companyData);

    // Update original values and reset dirty state
    this.originalCompanyValues = { ...companyData };
    this.isFormDirty = false;
    this.companyForm.markAsPristine();

    // Show success message or handle response
    alert('Company information saved successfully!');
  } else {
    console.log('Form is invalid - please check the errors above');
  }
}

onCompanyCancel(): void {
  this.companyForm.reset(this.originalCompanyValues);
  this.isFormDirty = false;
}

copy(value: string) {
  navigator.clipboard.writeText(value);
  this.shareService.showInfo('ID copied Sucessfully');
}
}
